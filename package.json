{"name": "joolun-plus-ui", "version": "2.0.5", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@smallwei/avue": "2.8.22", "animate.css": "^3.7.2", "avue-plugin-map": "0.0.8", "axios": "^0.19.0", "babel-polyfill": "^6.26.0", "benz-amr-recorder": "^1.0.14", "classlist-polyfill": "^1.2.0", "clipboard": "^2.0.4", "crypto-js": "^3.1.9-1", "echarts": "^4.9.0", "element-ui": "2.15.5", "font-awesome": "^4.7.0", "js-cookie": "^2.2.0", "jsencrypt": "3.0.0-rc.1", "moment": "^2.24.0", "nprogress": "^0.2.0", "sass": "^1.59.3", "script-loader": "^0.7.2", "sockjs-client": "^1.3.0", "stompjs": "^2.3.3", "swiper": "^5.4.2", "vue": "2.6.10", "vue-awesome-swiper": "^4.1.1", "vue-axios": "^2.1.4", "vue-clipboard2": "^0.3.0", "vue-count-to": "^1.0.13", "vue-cron": "^1.0.9", "vue-echarts": "^4.0.1", "vue-froala-wysiwyg": "^3.0.6", "vue-json-editor": "^1.2.3", "vue-json-tree-view": "^2.1.4", "vue-qr": "^2.1.0", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.1", "vue-video-player": "^5.0.2", "vuedraggable": "^2.23.2", "vuex": "^3.1.1", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.10.0", "@vue/cli-plugin-eslint": "^3.10.0", "@vue/cli-service": "^3.10.0", "chai": "^4.1.2", "less": "^3.9.0", "less-loader": "^4.1.0", "mockjs": "^1.0.1-beta3", "sass-loader": "^7.0.1", "vue-template-compiler": "2.6.10"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}, "eslintConfig": {"rules": {"no-console": "off"}}}