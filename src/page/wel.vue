<template>
    <div>
        <basic-container class="wel-view">
            <div class="box-card">
                <top-cards :top-crad-info="topCradInfo" />
                </div>

            <div class="box-card">
                <div class="sy-nrt">
                    <todo-list
                        :status0="status0"
                        :status1="status1"
                        :status2="status2"
                        :status4="status4"
                    />
                    <common-functions />
                </div>
            </div>

            <sales-trend />

            <div class="lastFlex">
                <!-- <search-data /> -->
                <sales-category />
                        </div>
        </basic-container>
    </div>
</template>

<script>
import TopCards from '@/components/dashboard/TopCards'
import TodoList from '@/components/dashboard/TodoList'
import CommonFunctions from '@/components/dashboard/CommonFunctions'
import SalesTrend from '@/components/dashboard/SalesTrend'
import SearchData from '@/components/dashboard/SearchData'
import SalesCategory from '@/components/dashboard/SalesCategory'

import {
    statistics,
    ordercount,
    saleamount,
    spusale,
} from "@/api/mall/largescreen";
import { getStatistics as getStatisticsGoodsSpu } from "@/api/mall/goodsspu";
import {
    getStatistics as getStatisticsOrderInfo,
    getPage as getOrderPage,
} from "@/api/mall/orderinfo";
import { getStatistics as getStatisticsUserInfo } from "@/api/mall/userinfo";
// import chinaMap from "echarts/map/json/china.json";  //中国地图
import chinaMap from "@/util/china.json"; //中国地图
import { mapGetters } from "vuex";

import { h5HostMobile, h5HostPC } from "@/config/env.js";
import { getStore } from "@/util/store";

import { getCount as userOnlineCount } from "@/api/mall/useronline";

export default {
    name: "wel",
    components: {
        TopCards,
        TodoList,
        CommonFunctions,
        SalesTrend,
        SearchData,
        SalesCategory
    },
    data() {
        return {
            topCradInfo: {
                orderInfo: {
                    orderDayCount: 0,
                    orderMonthCount: 0,
                    orderDayCountLastYear: 0,
                    orderDayCountGrowthRate: 0,
                    orderDayCountGrowthRateLastDay: 0,
                },
                goodsInfo: {
                    goodsDayCount: 0,
                    goodsMonthCount: 0,
                },
                userInfo: {
                    userCount: 0,
                    userDayCount: 0,
                    userMonthCount: 0,
                },
                userOnlineInfo: {
                    userOnlineCount: 0,
                },
                saleInfo: {
                    saleDayAmount: 0,
                    saleMonthAmount: 0,
                    // 去年今日数据
                    saleDayAmountLastYear: 0,
                    // 日同比增长率
                    saleDayAmountGrowthRate: 0,
                    //  日环比增长率
                    saleDayAmountGrowthRateLastDay: 0,
                },
            },
            status0: 0,
            status1: 0,
            status2: 0,
            status4: 0,
        };
    },
    computed: {
        ...mapGetters(["website", "userInfo"]),
    },
    created() {
        this.getMallSummary();
        // this.$notify({
        //     title: "欢迎登录",
        //     dangerouslyUseHTMLString: true,
        //     duration: 0,
        //     message: "<div>" + "<span>" + "" + "</span>" + "</div>",
        // });
        this.checkBrowser();
    },
    methods: {
        checkBrowser() {
            const userAgent = navigator.userAgent;
        let isIE =
            userAgent.indexOf("compatible") > -1 &&
            userAgent.indexOf("MSIE") > -1;
        let isSafari =
            userAgent.indexOf("Chrome") == -1 &&
            userAgent.indexOf("Safari") > -1;
        if (isIE || isSafari) {
            setTimeout(() => {
                this.$notify({
                    title: "浏览器兼容",
                    message:
                        "请使用谷歌、火狐、360极速等主流浏览器进行操作，否则页面可能出现未知错误！",
                    duration: 0,
                    type: "warning",
                });
            }, 500);
        }
        },
        async getMallSummary() {
            // 获取所有需要的时间范围
            const timeRanges = {
                today: {
                    begin: this.$moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    end: this.$moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
                },
                yesterday: {
                    begin: this.$moment().subtract(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    end: this.$moment().subtract(1, 'day').endOf('day').format('YYYY-MM-DD HH:mm:ss')
                },
                month: {
                    begin: this.$moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
                    end: this.$moment().endOf('month').format('YYYY-MM-DD HH:mm:ss')
                },
                lastYear: {
                    begin: this.$moment().subtract(1, 'year').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                    end: this.$moment().subtract(1, 'year').endOf('day').format('YYYY-MM-DD HH:mm:ss')
                }
            }

            // 获取订单状态数量
            this.getOrderTypeCount()

            // 计算数据总和
            const calculateTotal = (data) => {
                return data.shopInfo.length > 0 ? data.shopInfo.reduce((acc, curr) => acc + curr.ct, 0) : 0
            }

            // 计算增长率
            const calculateGrowthRate = (current, previous) => {
                return ((current - previous) / (previous || 1)) * 100
            }

            try {
                // 并行获取所有销售额数据
                const [
                    todaySales,
                    yesterdaySales,
                    monthSales,
                    lastYearSales,
                    todayOrders,
                    yesterdayOrders,
                    monthOrders,
                    lastYearOrders
                ] = await Promise.all([
                    this.getSaleAmount(timeRanges.today.begin, timeRanges.today.end),
                    this.getSaleAmount(timeRanges.yesterday.begin, timeRanges.yesterday.end),
                    this.getSaleAmount(timeRanges.month.begin, timeRanges.month.end),
                    this.getSaleAmount(timeRanges.lastYear.begin, timeRanges.lastYear.end),
                    this.getOrderCount(timeRanges.today.begin, timeRanges.today.end),
                    this.getOrderCount(timeRanges.yesterday.begin, timeRanges.yesterday.end),
                    this.getOrderCount(timeRanges.month.begin, timeRanges.month.end),
                    this.getOrderCount(timeRanges.lastYear.begin, timeRanges.lastYear.end)
                ])

                // 处理销售额数据
                const salesData = {
                    today: calculateTotal(todaySales),
                    yesterday: calculateTotal(yesterdaySales),
                    month: calculateTotal(monthSales),
                    lastYear: calculateTotal(lastYearSales)
                }

                // 处理订单数据
                const orderData = {
                    today: calculateTotal(todayOrders),
                    yesterday: calculateTotal(yesterdayOrders),
                    month: calculateTotal(monthOrders),
                    lastYear: calculateTotal(lastYearOrders)
                }

                // 更新销售额信息
                this.topCradInfo.saleInfo = {
                    saleDayAmount: salesData.today,
                    saleDayAmountLastDay: salesData.yesterday,
                    saleMonthAmount: salesData.month,
                    saleDayAmountLastYear: salesData.lastYear,
                    saleDayAmountGrowthRate: calculateGrowthRate(salesData.today, salesData.lastYear),
                    saleDayAmountGrowthRateLastDay: calculateGrowthRate(salesData.today, salesData.yesterday)
                }

                // 更新订单信息
                this.topCradInfo.orderInfo = {
                    orderDayCount: orderData.today,
                    orderDayCountLastDay: orderData.yesterday,
                    orderMonthCount: orderData.month,
                    orderDayCountLastYear: orderData.lastYear,
                    orderDayCountGrowthRate: calculateGrowthRate(orderData.today, orderData.lastYear),
                    orderDayCountGrowthRateLastDay: calculateGrowthRate(orderData.today, orderData.yesterday)
                }

                // 获取用户信息
                 this.getUserInfo()
            } catch (error) {
                console.error('获取数据失败:', error)
                this.$message.error('获取数据失败，请稍后重试')
            }
        },
        /**
         * 获取销售额
         * @param beginTime
         * @param endTime
         */
        getSaleAmount(beginTime, endTime) {
            return new Promise((resolve, reject) => {
                saleamount({ beginTime, endTime },false)
                    .then(({ data }) => {
                        resolve(data.data);
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        /**
         * 获取订单数
         * @param beginTime
         * @param endTime
         */
        getOrderCount(beginTime, endTime) {
            return new Promise((resolve, reject) => {
                ordercount({ beginTime, endTime },false)
                    .then(({ data }) => {
                        resolve(data.data);
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        /**
         * 获取商品统计-今日新增-全部商品
         */
        getGoodsSpu() {
            getStatisticsGoodsSpu().then((res) => {
                console.log(res, "res");
            });
        },
        /**
         * 获取订单统计-今日新增-全部订单
         */
        getOrderInfo() {
            getStatisticsOrderInfo().then((res) => {
                console.log(res, "res");
            });
        },
        /**
         * 获取用户统计-今日新增-全部用户
         */
        getUserInfo() {
            getStatisticsUserInfo().then(({ data }) => {
                console.log(data, "data");
                this.topCradInfo.userInfo.userCount = data.data.countTotal;
                this.topCradInfo.userInfo.userDayCount = data.data.countToday;
            });
        },
        /**
         * 获取在线数量-小程序
         * @param val
         */
        getUserOnlineCount() {
            userOnlineCount("MA").then((res) => {
                console.log(res, "res");
            });
        },

        /**
         * 获取订单类型数量
         */
        getOrderTypeCount() {
            getOrderPage({
                current: 1,
                size: 0,
                status: "0",
            },false).then((response) => {
                this.status0 = response.data.data.total;
            });
            getOrderPage({
                current: 1,
                size: 0,
                status: "1",
            },false).then((response) => {
                this.status1 = response.data.data.total;
            });
            getOrderPage({
                current: 1,
                size: 0,
                status: "2",
            },false).then((response) => {
                this.status2 = response.data.data.total;
            });
            getOrderPage({
                current: 1,
                size: 0,
                status: "4",
            },false).then((response) => {
                this.status4 = response.data.data.total;
            });
        },
    },
};
</script>

<style lang="scss">
.wel-view {
    .box-card {
        margin-top: 20px;
        padding: 0 30px;
        position: relative;
    }

    .sy-nrt {
        display: flex;
        padding: 50px 0;
    }

    .lastFlex {
        display: flex;
        height: 420px;
        margin-top: 40px;
    }
}
</style>
