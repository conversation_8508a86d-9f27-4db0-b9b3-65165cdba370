/**
 * 全站http配置
 *
 * header参数说明
 * serialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios'
import store from '@/store'
import router from '@/router/router'
import {serialize, judgePlatformPage} from '@/util/util'
import NProgress from 'nprogress' // progress bar
import errorCode from '@/const/errorCode'
import {Message, MessageBox, Loading} from 'element-ui'
import 'nprogress/nprogress.css'
import {getStore, setStore} from '@/util/store'

axios.defaults.timeout = 30000
// 返回其他状态吗
axios.defaults.validateStatus = function (status) {
    return status // 默认的
}
// 跨域请求，允许保存cookie
axios.defaults.withCredentials = true
// NProgress Configuration
NProgress.configure({
    showSpinner: false
})

let loadingInstance = null;

function loadingStart() {
    loadingInstance = Loading.service({
        lock: true,
        text: "正在加载数据,请稍后...",
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
    })
}

function loadingClose() {
    loadingInstance && loadingInstance.close()
}

// HTTPrequest拦截
axios.interceptors.request.use(config => {
    // console.log(config.showLoading, "config");
    if (config.showLoading !== false) {
        loadingStart()
        NProgress.start() // start progress bar
    }
    const isToken = (config.headers || {}).isToken === false
    let token = store.getters.access_token
    if (token && !isToken) {
        config.headers['Authorization'] = 'Bearer ' + token// token
    }
    let switchTenantId = getStore({name: 'switchTenantId'})
    if (switchTenantId && !judgePlatformPage()
        && config.url != "/upms/menu/tree") {
        //只有非平台系统页面传切换租户ID
        config.headers['switch-tenant-id'] = switchTenantId // 切换租户ID
    }
    return config
}, error => {
    return Promise.reject(error)
})


// HTTPresponse拦截
axios.interceptors.response.use(res => {
    NProgress.done()
    loadingClose()
    const status = Number(res.status) || 200
    const message = res.data.msg || errorCode[status] || errorCode['default']
    if (status === 401 && res.data.data === 'invalid_token') {
        Message({
            message: '登录过期，请重新登录',
            type: 'error'
        })
        store.dispatch('FedLogOut').then(() => {
            router.push({path: '/login'})
        })
        return
    }

    if (status !== 200 || res.data.code === 1) {
        Message({
            message: message,
            type: 'error'
        })
        return Promise.reject(new Error(message))
    }

    return res
}, error => {
    NProgress.done()
    return Promise.reject(new Error(error))
})

export default axios
