<template>
    <div class="delivery-map-container">
        <div class="map-controls" v-if="showControl && row.type != 'view'">
            <div class="area-select">
                <el-cascader
                    v-model="selectedArea"
                    :options="areaOptions"
                    :props="{
                        value: 'adcode',
                        label: 'name',
                        children: 'districtList',
                    }"
                    placeholder="请选择省市区"
                    @change="handleAreaChange"
                />
            </div>
            <div class="place-search">
                <el-autocomplete
                    v-model="searchKeyword"
                    :fetch-suggestions="querySearchAsync"
                    placeholder="请输入地点"
                    @select="handlePlaceSelect"
                    :trigger-on-focus="false"
                    autosize
                    clearable
                >
                    <template slot-scope="{ item }">
                        <div class="suggestion-item">
                            <i class="el-icon-location"></i>
                            <div class="suggestion-content">
                                <div class="name">{{ item.name }}</div>
                                <div class="district">{{ item.district }}</div>
                            </div>
                        </div>
                    </template>
                </el-autocomplete>
            </div>
            <div class="center-point">
                <span>中心点：</span>
                <el-input
                    style="width: 80px"
                    v-model="currentPositionIpt[0]"
                    size="mini"
                />
                <el-input
                    style="width: 80px"
                    v-model="currentPositionIpt[1]"
                    size="mini"
                />
                <el-button type="primary" size="mini" @click="handleCenterPoint"
                    >确定</el-button
                >
            </div>
        </div>
        <div id="deliveryMap" :style="{ width: width, height: height }"></div>
        <div class="map-center" v-show="showControl">
            <span>中心点：{{ currentPosition.join(", ") }}</span>
            <!-- toolip -->
            <el-tooltip
                effect="dark"
                :content="currentPoints.join(', ')"
                v-if="rangeType === 'polygon'"
                placement="top"
            >
                <div class="polygon-points">
                    顶点：{{ currentPoints.join(", ") }}
                </div>
            </el-tooltip>
            <span v-if="rangeType === 'circle'">
                配送半径：
                <el-input-number
                    size="mini"
                    v-model="radius"
                    :min="0"
                    :max="100000"
                    :step="100"
                    :precision="0"
                    :controls="false"
                />
                米
            </span>
            <el-button
                v-if="rangeType === 'circle'"
                type="primary"
                size="mini"
                @click="confirmRange"
            >
                确认
            </el-button>
        </div>
    </div>
</template>

<script>
import initMapLoader from "@/util/map";

export default {
    name: "DeliveryMap",
    props: {
        width: {
            type: String,
            default: "100%",
        },
        height: {
            type: String,
            default: "400px",
        },
        rangeType: {
            type: String,
            default: "polygon", // circle 或 polygon
        },
        showControl: {
            type: Boolean,
            default: true,
        },
        row: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            map: null,
            AMap: null,
            circle: null,
            polygon: null,
            circleEditor: null,
            polygonEditor: null,
            currentPosition: [116.397428, 39.90923], // 默认北京位置
            currentPositionIpt: [116.397428, 39.90923], // 默认北京位置
            currentPoints: [], // 多边形顶点
            selectedArea: [], // 选中的省市区
            areaOptions: [], // 省市区数据
            district: null, // 行政区对象
            searchKeyword: "", // 搜索关键词
            placeSearch: null, // 地点搜索对象
            radius: 5000, // 默认配送半径
            defaultPolygonPoints: [
                [116.768882, 38.339167],
                [116.756265, 38.321629],
                [116.752746, 38.31163],
                [116.75236, 38.295853],
                [116.750793, 38.283651],
                [116.745204, 38.268385],
                [116.771701, 38.269623],
                [116.793392, 38.27086],
                [116.840208, 38.262552],
                [116.897458, 38.257026],
                [116.917575, 38.255189],
                [116.934945, 38.261979],
                [116.954032, 38.266343],
                [116.968999, 38.275019],
                [116.969471, 38.304862],
                [116.965008, 38.330652],
                [116.94973, 38.343274],
                [116.939002, 38.346353],
                [116.927586, 38.346201],
                [116.890292, 38.346403],
                [116.872675, 38.347581],
                [116.852311, 38.348759],
                [116.813472, 38.354076],
                [116.774633, 38.356701],
            ],
        };
    },
    mounted() {
        console.log("DeliveryMap mounted");
        // this.initMap();
    },
    methods: {
        async initMap() {
            try {
                const mapInfo = await initMapLoader();
                this.AMap = mapInfo;

                // 初始化地图
                this.map = new mapInfo.Map("deliveryMap", {
                    zoom: 11,
                    center: this.currentPosition,
                });

                // 加载插件
                await this.loadPlugins();

                // 加载省市区数据
                await this.loadAreaData();

                // 通知父组件地图加载完成
                this.$emit("load", {
                    AMap: mapInfo,
                    map: this.map,
                    circle: this.circle,
                    polygon: this.polygon,
                });
            } catch (error) {
                console.error("地图加载失败:", error);
                this.$message.error("地图加载失败");
            }
        },
        setPolygonRange() {
            this.currentPosition = [116.857461, 38.310582];
            this.createPolygon(this.defaultPolygonPoints);
        },
        loadPlugins() {
            return new Promise((resolve) => {
                this.AMap.plugin(
                    [
                        "AMap.ToolBar",
                        "AMap.Scale",
                        "AMap.Geolocation",
                        "AMap.CircleEditor",
                        "AMap.PolygonEditor",
                        "AMap.CitySearch",
                        "AMap.DistrictSearch",
                        "AMap.PlaceSearch",
                        "AMap.AutoComplete",
                    ],
                    () => {
                        // 添加地图控件
                        this.map.addControl(
                            new this.AMap.ToolBar({
                                position: {
                                    top: "10px",
                                    right: "10px",
                                },
                            })
                        );
                        this.map.addControl(
                            new this.AMap.Scale({
                                position: {
                                    bottom: "10px",
                                    left: "10px",
                                },
                            })
                        );

                        // 定位到当前位置
                        const geolocation = new this.AMap.Geolocation({
                            enableHighAccuracy: true,
                            timeout: 10000,
                            buttonPosition: "RB",
                            buttonOffset: new this.AMap.Pixel(10, 10),
                            zoomToAccuracy: true,
                            convert: true,
                            showButton: true,
                            showMarker: true,
                            showCircle: true,
                            panToLocation: true,
                            useNative: false,
                            GeoLocationFirst: true,
                            needAddress: true,
                            extensions: "all",
                        });

                        // 添加定位错误处理
                        geolocation.on("error", (error) => {
                            console.warn("浏览器定位失败，尝试IP定位:", error);
                            // 浏览器定位失败时，使用IP定位
                            const citySearch = new this.AMap.CitySearch();
                            citySearch.getLocalCity((status, result) => {
                                if (
                                    status === "complete" &&
                                    result.info === "OK"
                                ) {
                                    // 定位成功
                                    const { center } = result.rectangle
                                        .split(";")[0]
                                        .split(",");
                                    const position = center.split(",");
                                    this.currentPosition = position;
                                    this.map.setCenter(position);
                                    this.map.setZoom(11);
                                } else {
                                    // IP定位也失败，使用默认位置
                                    console.warn("IP定位失败，使用默认位置");
                                    this.map.setCenter(this.currentPosition);
                                    this.map.setZoom(11);
                                }
                            });
                        });

                        // 添加定位成功处理
                        geolocation.on("complete", (data) => {
                            if (data.position) {
                                this.currentPosition = [
                                    data.position.lng,
                                    data.position.lat,
                                ];
                                this.map.setCenter(this.currentPosition);
                                this.map.setZoom(11);
                            }
                        });

                        this.map.addControl(geolocation);

                        // 初始化地点搜索
                        this.placeSearch = new this.AMap.PlaceSearch({
                            pageSize: 10,
                            pageIndex: 1,
                            extensions: "all",
                        });

                        // 延迟执行定位，避免初始化时的权限问题
                        setTimeout(() => {
                            geolocation.getCurrentPosition();
                            resolve();
                        }, 800);
                    }
                );
            });
        },
        // 加载省市区数据
        loadAreaData() {
            return new Promise((resolve) => {
                const districtSearch = new this.AMap.DistrictSearch({
                    level: "province",
                    subdistrict: 3,
                });
                districtSearch.search("中国", (status, result) => {
                    if (
                        status === "complete" &&
                        result.districtList &&
                        result.districtList[0] &&
                        result.districtList[0].districtList
                    ) {
                        this.areaOptions = result.districtList[0].districtList;
                        resolve();
                    } else {
                        console.error("加载省市区数据失败:", result);
                        this.$message.error("加载省市区数据失败");
                        resolve();
                    }
                });
            });
        },
        // 处理区域选择变化
        handleAreaChange(value) {
            if (!value || value.length === 0) return;

            const districtSearch = new this.AMap.DistrictSearch({
                level: "district",
                subdistrict: 0,
            });

            // 使用最后一级的行政区编码
            const districtCode = value[value.length - 1];
            districtSearch.search(districtCode, (status, result) => {
                if (
                    status === "complete" &&
                    result.districtList &&
                    result.districtList[0]
                ) {
                    const district = result.districtList[0];
                    // 使用行政区的中心点
                    this.currentPosition = [
                        district.center.lng,
                        district.center.lat,
                    ];
                    this.map.setCenter(this.currentPosition);

                    // 根据范围类型绘制默认范围
                    if (this.rangeType === "circle") {
                        this.createCircle();
                    } else {
                        this.createPolygon();
                    }
                } else {
                    console.error("获取区域信息失败:", result);
                    this.$message.error("获取区域信息失败");
                }
            });
        },
        // 创建圆形范围
        createCircle(center = null, radius = null) {
            this.clearAll();

            // 使用当前定位位置或默认位置
            const circleCenter = center || this.currentPosition;
            const circleRadius = radius || this.radius;
            if (center) {
                this.currentPosition = center;
            }
            if (radius) {
                this.radius = radius;
            }

            this.circle = new this.AMap.Circle({
                center: circleCenter,
                radius: circleRadius,
                strokeColor: "#FF0000",
                strokeWeight: 2,
                strokeOpacity: 0.8,
                fillColor: "#FF0000",
                fillOpacity: 0.2,
                zIndex: 50,
            });

            this.circle.setMap(this.map);
            this.map.setCenter(circleCenter);
            this.map.setZoom(11);

            // 创建编辑器
            this.circleEditor = new this.AMap.CircleEditor(
                this.map,
                this.circle
            );
            this.circleEditor.open();

            // 监听编辑完成事件
            this.circleEditor.on("adjust", () => {
                const center = this.circle.getCenter();
                const radius = this.circle.getRadius();
                this.currentPosition = [center.lng, center.lat];
                this.radius = radius;
                // 触发draw事件
                this.$emit("draw", {
                    type: "circle",
                    center: [center.lng, center.lat],
                    radius: radius,
                });
            });

            // 监听移动事件
            this.circleEditor.on("move", () => {
                const center = this.circle.getCenter();
                this.currentPosition = [center.lng, center.lat];
                // 触发draw事件
                this.$emit("draw", {
                    type: "circle",
                    center: [center.lng, center.lat],
                    radius: this.radius,
                });
            });
        },
        // 创建多边形范围
        createPolygon(points = null, currentPosition = null) {
            this.clearAll();

            // 使用当前定位位置创建默认矩形
            const center = points ? null : this.currentPosition;
            const defaultPoints = center
                ? [
                      [center[0] - 0.1, center[1] - 0.1], // 约5000米
                      [center[0] - 0.1, center[1] + 0.1],
                      [center[0] + 0.1, center[1] + 0.1],
                      [center[0] + 0.1, center[1] - 0.1],
                  ]
                : points;

            if (points) {
                this.currentPoints = points;
            } else {
                this.currentPoints = defaultPoints;
            }
            if (currentPosition) {
                this.currentPosition = currentPosition;
            }

            this.polygon = new this.AMap.Polygon({
                path: defaultPoints,
                strokeColor: "#FF0000",
                strokeWeight: 2,
                strokeOpacity: 0.8,
                fillColor: "#FF0000",
                fillOpacity: 0.2,
                zIndex: 50,
            });

            this.polygon.setMap(this.map);
            this.map.setCenter(this.currentPosition);
            this.map.setZoom(11);
            this.map.setFitView();

            // 创建编辑器
            this.polygonEditor = new this.AMap.PolygonEditor(
                this.map,
                this.polygon
            );
            if (this.row.type != 'view') {
                this.polygonEditor.open();
            }

            // 监听编辑完成事件
            this.polygonEditor.on("adjust", () => {
                const path = this.polygon.getPath();
                // 计算多边形的中心点
                const bounds = this.polygon.getBounds();
                const center = bounds.getCenter();
                this.currentPosition = [center.lng, center.lat];

                this.currentPoints = points
                    ? this.polygon.getPath()
                    : path.map((point) => [point.lng, point.lat]);

                this.$emit("draw", {
                    type: "polygon",
                    center: [center.lng, center.lat],
                    points: this.currentPoints,
                });
            });
        },
        // 清除所有绘制
        clearAll() {
            if (this.circle) {
                this.circle.setMap(null);
                this.circle = null;
            }
            if (this.polygon) {
                this.polygon.setMap(null);
                this.polygon = null;
            }
            if (this.circleEditor) {
                this.circleEditor.close();
                this.circleEditor = null;
            }
            if (this.polygonEditor) {
                this.polygonEditor.close();
                this.polygonEditor = null;
            }
        },
        // 异步获取搜索建议
        querySearchAsync(queryString, callback) {
            if (!queryString) {
                callback([]);
                return;
            }

            const autoComplete = new this.AMap.AutoComplete({
                city: "全国",
            });

            autoComplete.search(queryString, (status, result) => {
                if (status === "complete" && result.tips) {
                    const suggestions = result.tips.map((tip) => ({
                        value: tip.name,
                        name: tip.name,
                        district: tip.district,
                        location: tip.location,
                    }));
                    callback(suggestions);
                } else {
                    callback([]);
                }
            });
        },
        // 处理地点选择
        handlePlaceSelect(item) {
            if (item.location) {
                this.currentPosition = [item.location.lng, item.location.lat];
                this.map.setCenter(this.currentPosition);
                this.map.setZoom(15);

                // 根据范围类型绘制默认范围
                if (this.rangeType === "circle") {
                    this.createCircle();
                } else {
                    this.createPolygon();
                }
            }
        },
        // 确认配送范围
        confirmRange() {
            if (this.rangeType === "circle") {
                if (!this.circle) {
                    this.$message.warning("请先绘制配送范围");
                    return;
                }
                const center = this.circle.getCenter();
                const radius = this.circle.getRadius();
                this.$emit("confirm", {
                    type: "circle",
                    center: [center.lng, center.lat],
                    radius: radius,
                });
            } else {
                if (!this.polygon) {
                    this.$message.warning("请先绘制配送范围");
                    return;
                }
                const path = this.polygon.getPath();
                this.$emit("confirm", {
                    type: "polygon",
                    center: this.currentPosition,
                    points:
                        this.currentPoints.length > 0
                            ? this.currentPoints
                            : path.map((point) => [point.lng, point.lat]),
                });
            }
        },
        // 监听半径变化
        watchRadius() {
            if (this.circle) {
                // 获取当前中心点
                const center = this.circle.getCenter();
                // 重新创建圆形
                this.createCircle([center.lng, center.lat], this.radius);
            }
        },
        watchCurrentPosition() {
            this.currentPositionIpt = [
                this.currentPosition[0],
                this.currentPosition[1],
            ];
        },
        handleCenterPoint() {
            this.currentPosition = [
                +this.currentPositionIpt[0],
                +this.currentPositionIpt[1],
            ];
            // 根据范围类型绘制默认范围
            if (this.rangeType === "circle") {
                this.createCircle();
            } else {
                this.createPolygon();
            }
        },
    },
    watch: {
        radius: {
            handler: "watchRadius",
            immediate: true,
        },
        currentPosition: {
            handler: "watchCurrentPosition",
            immediate: true,
        },
    },
    beforeDestroy() {
        this.clearAll();
        if (this.district) {
            this.district.setMap(null);
        }
        if (this.map) {
            this.map.destroy();
        }
    },
};
</script>

<style lang="less" scoped>
.delivery-map-container {
    position: relative;
    .map-controls {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 100;
        display: flex;
        flex-direction: column;
        gap: 10px;
        background: #fff;
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        animation: showControl 0.3s ease-in-out;

        @keyframes showControl {
            0% {
                opacity: 0;
                transform: translateY(-10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .area-select {
        }

        .place-search {
        }

        .center-point {
            display: flex;
            align-items: center;
            gap: 10px;
        }
    }

    #deliveryMap {
        border-radius: 4px;
        overflow: hidden;
    }
}

.suggestion-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 4px 0;
    min-width: 240px;

    i {
        color: #409eff;
        margin-top: 2px;
    }

    .suggestion-content {
        flex: 1;
        min-width: 0; // 防止内容溢出

        .name {
            font-size: 14px;
            color: #303133;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .district {
            font-size: 12px;
            color: #909399;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.map-center {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
    display: flex;
    align-items: center;
    gap: 15px;
    background: #fff;
    padding: 8px 15px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    animation: showControl2 0.3s ease-in-out;
    .polygon-points {
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 200px;
    }

    @keyframes showControl2 {
        0% {
            opacity: 0;
            transform: translateY(-10px) translateX(-50%);
        }
        100% {
            opacity: 1;
            transform: translateY(0) translateX(-50%);
        }
    }

    span {
        font-size: 14px;
        color: #606266;
        white-space: nowrap;

        .el-input-number {
            width: 100px;
            margin: 0 5px;
        }
    }

    .el-button {
        margin-left: 10px;
    }
}
</style>
