<template>
    <div class="freight-template-table">
        <el-table
            :data="tableData"
            border
            style="width: 100%"
            v-loading="loading"
            @selection-change="handleSelectionChange"
        >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column
                prop="id"
                label="ID"
                width="80"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="name"
                label="名称"
                min-width="150"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="freeShippingAmount"
                label="满额包邮"
                width="120"
                align="center"
            >
                <template slot-scope="scope">
                    ¥{{ scope.row.freeShippingAmount }}
                </template>
            </el-table-column>
            <el-table-column
                prop="minOrderAmount"
                label="满额起送"
                width="120"
                align="center"
            >
                <template slot-scope="scope">
                    ¥{{ scope.row.minOrderAmount }}
                </template>
            </el-table-column>
            <el-table-column
                prop="minOrderCount"
                label="满件起送"
                width="120"
                align="center"
            >
                <template slot-scope="scope">
                    {{ scope.row.minOrderCount }}件
                </template>
            </el-table-column>
            <el-table-column
                prop="deliveryTime"
                label="配送时间"
                width="150"
                align="center"
            >
                <template slot-scope="scope">
                    {{ scope.row.deliveryTime }}分钟
                </template>
            </el-table-column>
            <el-table-column
                prop="deliveryRange"
                label="配送范围"
                width="120"
                align="center"
            >
                <template slot-scope="scope">
                    <el-tag :type="getRangeType(scope.row.deliveryRange)">
                        {{ getRangeText(scope.row.deliveryRange) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="状态"
                width="100"
                align="center"
            >
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
                        {{ scope.row.status === '1' ? '开启' : '关闭' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="150"
                align="center"
                fixed="right"
            >
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        size="small"
                        @click="handleEdit(scope.row)"
                    >编辑</el-button>
                    <el-button
                        type="text"
                        size="small"
                        @click="handleDelete(scope.row)"
                    >删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'FreightTemplateTable',
    props: {
        tableData: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        handleSelectionChange(val) {
            this.$emit('selection-change', val)
        },
        handleEdit(row) {
            this.$emit('edit', row)
        },
        handleDelete(row) {
            this.$emit('delete', row)
        },
        getRangeType(range) {
            if (!range) return 'info'
            return range.type === 'circle' ? 'success' : 'warning'
        },
        getRangeText(range) {
            if (!range) return '未设置'
            return range.type === 'circle' ? '圆形' : '多边形'
        }
    }
}
</script>

<style lang="less" scoped>
.freight-template-table {
    /deep/ .el-table th > .cell {
        color: #000;
        font-weight: bold;
    }
}
</style>
