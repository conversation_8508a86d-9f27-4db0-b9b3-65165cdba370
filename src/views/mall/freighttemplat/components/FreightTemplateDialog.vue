<template>
    <el-dialog
        :title="dialogType === 'add' ? '添加运费模板' : '编辑运费模板'"
        :visible.sync="visible"
        width="70%"
        :before-close="handleClose"
    >
        <el-form
            :model="form"
            :rules="rules"
            ref="form"
            label-width="120px"
        >
            <el-form-item label="模板名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入模板名称"></el-input>
            </el-form-item>
            <el-form-item label="满额包邮" prop="freeShippingAmount">
                <el-input-number
                    v-model="form.freeShippingAmount"
                    :min="0"
                    :precision="2"
                    :step="10"
                    controls-position="right"
                ></el-input-number>
                <span class="form-tip">元，0表示不包邮</span>
            </el-form-item>
            <el-form-item label="满额起送" prop="minOrderAmount">
                <el-input-number
                    v-model="form.minOrderAmount"
                    :min="0"
                    :precision="2"
                    :step="10"
                    controls-position="right"
                ></el-input-number>
                <span class="form-tip">元，0表示不限制</span>
            </el-form-item>
            <el-form-item label="满件起送" prop="minOrderCount">
                <el-input-number
                    v-model="form.minOrderCount"
                    :min="0"
                    :step="1"
                    controls-position="right"
                ></el-input-number>
                <span class="form-tip">件，0表示不限制</span>
            </el-form-item>
            <el-form-item label="配送时间设置">
                <div class="delivery-time-header">
                    <el-radio-group v-model="form.deliveryTimeEnabled">
                        <el-radio :label="false">关闭</el-radio>
                        <el-radio :label="true">开启</el-radio>
                    </el-radio-group>
                    <span class="delivery-time-tip">开启后用户下单时可以选择配送时间或提货时间</span>
                </div>
                <div v-if="form.deliveryTimeEnabled">
                    <div v-for="(item, idx) in form.deliveryTimeList" :key="idx" class="delivery-time-row">
                        <el-select v-model="item.day" class="delivery-time-select" size="mini">
                            <el-option label="当天" value="当天" />
                            <el-option label="第2天" value="第2天" />
                            <el-option label="第3天" value="第3天" />
                            <el-option label="第4天" value="第4天" />
                            <el-option label="第5天" value="第5天" />
                            <el-option label="第6天" value="第6天" />
                            <el-option label="第7天" value="第7天" />
                            <el-option label="第8天" value="第8天" />
                            <el-option label="第9天" value="第9天" />
                            <el-option label="第10天" value="第10天" />
                            <el-option label="第11天" value="第11天" />
                            <el-option label="第12天" value="第12天" />
                            <el-option label="第13天" value="第13天" />
                            <el-option label="第14天" value="第14天" />
                            <el-option label="第15天" value="第15天" />
                        </el-select>
                        <el-input-number size="mini" v-model="item.startHour" :min="0" :max="23" class="delivery-time-input" />
                        <span>点</span>
                        <el-input-number size="mini" v-model="item.startMinute" :min="0" :max="59" class="delivery-time-input" />
                        <span>分 到</span>
                        <el-input-number size="mini" v-model="item.endHour" :min="0" :max="23" class="delivery-time-input" />
                        <span>点</span>
                        <el-input-number size="mini" v-model="item.endMinute" :min="0" :max="59" class="delivery-time-input" />
                        <span>分</span>
                        <el-button icon="el-icon-plus" size="mini" @click="addDeliveryTime" class="delivery-time-btn" v-if="idx === form.deliveryTimeList.length - 1"></el-button>
                        <el-button icon="el-icon-minus" size="mini" @click="removeDeliveryTime(idx)" class="delivery-time-btn" v-if="form.deliveryTimeList.length > 1"></el-button>
                    </div>
                    <div class="delivery-time-condition">
                        <span class="delivery-time-condition-tip">可选条件：</span>
                        <span>下单时间早于可选时间</span>
                        <el-input-number size="mini" v-model="form.deliveryTimeCondition" :min="0" class="delivery-time-input" />
                        <span>小时以上</span>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="配送范围" prop="deliveryRange">
                <div class="map-container">
                    <div class="map-tools">
                        <el-radio-group v-model="rangeType" @change="handleRangeTypeChange">
                            <el-radio label="circle">圆形范围</el-radio>
                            <el-radio label="polygon">多边形范围</el-radio>
                        </el-radio-group>
                        <div>
                            <el-button type="primary" size="small" @click="showControl = !showControl">
                                {{ showControl ? '隐藏工具栏' : '显示工具栏' }}
                            </el-button>
                            <el-button type="primary" size="small" @click="clearRange">清除范围</el-button>
                        </div>
                    </div>
                    <delivery-map
                        ref="deliveryMap"
                        width="100%"
                        height="400px"
                        :range-type="rangeType"
                        :show-control="showControl"
                        @load="handleMapLoad"
                        @draw="handleDraw"
                    />
                </div>
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-switch
                    v-model="form.status"
                    active-value="1"
                    inactive-value="0"
                ></el-switch>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import DeliveryMap from './DeliveryMap.vue'

export default {
    name: 'FreightTemplateDialog',
    components: {
        DeliveryMap
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        dialogType: {
            type: String,
            default: 'add'
        },
        editData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            loading: false,
            rangeType: 'circle',
            map: null,
            AMap: null,
            circle: null,
            polygon: null,
            mouseTool: null,
            showControl: true,
            form: {
                name: '',
                freeShippingAmount: 0,
                minOrderAmount: 0,
                minOrderCount: 0,
                deliveryTime: 30,
                status: '1',
                deliveryRange: {
                    type: 'circle',
                    center: null,
                    radius: null,
                    points: []
                },
                deliveryTimeEnabled: false,
                deliveryTimeList: [
                    { day: '当天', startHour: 12, startMinute: 0, endHour: 12, endMinute: 30 },
                    { day: '当天', startHour: 18, startMinute: 0, endHour: 18, endMinute: 30 },
                ],
                deliveryTimeCondition: 4
            },
            rules: {
                name: [
                    { required: true, message: '请输入模板名称', trigger: 'blur' },
                    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
                ],
                freeShippingAmount: [
                    { required: true, message: '请输入满额包邮金额', trigger: 'blur' }
                ],
                minOrderAmount: [
                    { required: true, message: '请输入满额起送金额', trigger: 'blur' }
                ],
                minOrderCount: [
                    { required: true, message: '请输入满件起送数量', trigger: 'blur' }
                ],
                status: [
                    { required: true, message: '请选择状态', trigger: 'change' }
                ]
            }
        }
    },
    watch: {
        visible(val) {
            if (val && this.dialogType === 'edit') {
                this.form = { ...this.editData }
                if (this.form.deliveryRange) {
                    this.rangeType = this.form.deliveryRange.type
                    this.$nextTick(() => {
                        this.drawRange()
                    })
                }
            }
        }
    },
    methods: {
        handleMapLoad({ AMap, map, mouseTool }) {
            this.AMap = AMap
            this.map = map
            this.mouseTool = mouseTool

            // 如果是编辑模式且有配送范围数据，则绘制范围
            if (this.dialogType === 'edit' && this.form.deliveryRange) {
                this.$nextTick(() => {
                    this.drawRange()
                })
            } else {
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.handleRangeTypeChange('circle')// 默认绘制圆形范围
                    }, 500)
                })
            }
        },
        // 绘制范围
        drawRange () {
            console.log(this.form.deliveryRange, 'this.form.deliveryRange')
            if (this.form.deliveryRange.type === 'circle') {
                this.$refs.deliveryMap.createCircle(this.form.deliveryRange.center, this.form.deliveryRange.radius)
            } else if (this.form.deliveryRange.type === 'polygon') {
                this.$refs.deliveryMap.createPolygon(this.form.deliveryRange.points)
            }
        },
        handleRangeTypeChange(type) {
            this.clearRange()
            this.form.deliveryRange.type = type
            if (type === 'circle') {
                this.$refs.deliveryMap.createCircle()
            } else if (type === 'polygon') {
                this.$refs.deliveryMap.createPolygon()
            }
        },
        clearRange() {
            this.$refs.deliveryMap.clearAll()
            this.form.deliveryRange = {
                type: this.rangeType,
                center: null,
                radius: null,
                points: []
            }
        },
        handleDraw(data) {
            if (data.type === 'circle') {
                this.form.deliveryRange = {
                    type: 'circle',
                    center: data.center,
                    radius: data.radius
                }
            } else if (data.type === 'polygon') {
                this.form.deliveryRange = {
                    type: 'polygon',
                    points: data.points
                }
            }
        },
        handleClose() {
            this.clearRange()
            this.$refs.form.resetFields()
            this.$emit('update:visible', false)
        },
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    // 获取范围数据
                    const circle = this.$refs.deliveryMap.circle
                    const polygon = this.$refs.deliveryMap.polygon
                    if (this.rangeType === 'circle' && circle) {
                        const center = circle.getCenter()
                        const radius = circle.getRadius()
                        this.form.deliveryRange = {
                            type: 'circle',
                            center: [center.lng, center.lat],
                            radius: radius
                        }
                    } else if (this.rangeType === 'polygon' && polygon) {
                        const path = polygon.getPath()
                        this.form.deliveryRange = {
                            type: 'polygon',
                            points: path.map(point => [point.lng, point.lat])
                        }
                    }

                    this.loading = true
                    this.$emit('submit', {
                        type: this.dialogType,
                        data: this.form
                    })
                    console.log(this.form, 'this.form')
                    console.log(this.dialogType, 'this.rangeType')
                }
            })
        },
        addDeliveryTime() {
            this.form.deliveryTimeList.push({ day: '当天', startHour: 12, startMinute: 0, endHour: 12, endMinute: 30 })
        },
        removeDeliveryTime(idx) {
            if (this.form.deliveryTimeList.length > 1) {
                this.form.deliveryTimeList.splice(idx, 1)
            }
        }
    },
    mounted() {
        // 添加地图绘制工具
        this.$nextTick(() => {
            if (this.AMap) {
                this.AMap.plugin(['AMap.MouseTool'], () => {
                    const mouseTool = new this.AMap.MouseTool(this.map)

                    // 监听绘制完成事件
                    mouseTool.on('draw', ({ obj }) => {
                        if (this.rangeType === 'circle') {
                            this.clearRange()
                            this.circle = obj
                            const center = obj.getCenter()
                            const radius = obj.getRadius()
                            this.form.deliveryRange = {
                                type: 'circle',
                                center: [center.lng, center.lat],
                                radius: radius
                            }
                        } else if (this.rangeType === 'polygon') {
                            this.clearRange()
                            this.polygon = obj
                            const path = obj.getPath()
                            this.form.deliveryRange = {
                                type: 'polygon',
                                points: path.map(point => [point.lng, point.lat])
                            }
                        }
                    })

                    // 监听范围类型变化
                    this.$watch('rangeType', (type) => {
                        if (type === 'circle') {
                            mouseTool.circle()
                        } else if (type === 'polygon') {
                            mouseTool.polygon()
                        }
                    })
                })
            }
        })
    }
}
</script>

<style lang="less" scoped>
.form-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
}

.map-container {
    border: 1px solid #DCDFE6;
    border-radius: 4px;

    .map-tools {
        padding: 10px;
        border-bottom: 1px solid #DCDFE6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}

// 新增 配送时间设置样式
.delivery-time-header {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}
.delivery-time-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
}
.delivery-time-row {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}
.delivery-time-select {
    width: 80px;
}
.delivery-time-input {
    // width: 60px;
    margin: 0 5px;
}
.delivery-time-btn {
    margin-left: 6px;
}
.delivery-time-condition {
    margin-top: 8px;
    display: flex;
    align-items: center;
    font-size: 14px;
}
.delivery-time-condition-tip {
    color: #909399;
    font-size: 12px;
    margin-right: 4px;
}
</style>
