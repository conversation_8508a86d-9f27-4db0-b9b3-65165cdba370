<template>
    <div class="freight-template-container">
        <div class="p-10 top">
            <div class="left">
                <el-button type="primary" size="small" @click="handleAdd"
                    >添加</el-button
                >
                <el-button type="danger" size="small" @click="handleBatchDelete"
                    >删除</el-button
                >
            </div>
            <div class="right">
                <div>
                    <span>名称</span>
                    <el-input
                        v-model="searchName"
                        placeholder="请输入模板名称"
                        style="width: 200px"
                        size="small"
                        @input="handleSearch"
                    ></el-input>
                </div>
                <div>
                    <span>状态</span>
                    <el-select
                        size="small"
                        v-model="status"
                        placeholder="请选择"
                        @change="handleSearch"
                    >
                        <el-option label="全部" value=""></el-option>
                        <el-option label="开启" value="1"></el-option>
                        <el-option label="关闭" value="0"></el-option>
                    </el-select>
                </div>
            </div>
        </div>

        <div class="p-10">
            <freight-template-table
                :table-data="tableData"
                :loading="tableLoading"
                @selection-change="handleSelectionChange"
                @edit="handleEdit"
                @delete="handleDelete"
            />
            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total"
                >
                </el-pagination>
            </div>
        </div>

        <freight-template-dialog
            :visible.sync="dialogVisible"
            :dialog-type="dialogType"
            :edit-data="editData"
            @submit="handleDialogSubmit"
        />
    </div>
</template>

<script>
import FreightTemplateTable from "./components/FreightTemplateTable.vue";
import FreightTemplateDialog from "./components/FreightTemplateDialog.vue";

export default {
    name: "FreightTemplate",
    components: {
        FreightTemplateTable,
        FreightTemplateDialog,
    },
    data() {
        return {
            tableData: [
                {
                    id: 1,
                    name: "默认运费模板",
                    freeShippingAmount: 99,
                    minOrderAmount: 20,
                    minOrderCount: 1,
                    deliveryTime: 30,
                    status: "1",
                    deliveryRange: {
                        type: "circle",
                        center: [116.397428, 39.90923],
                        radius: 3000,
                    },
                },
                {
                    id: 2,
                    name: "同城配送模板",
                    freeShippingAmount: 199,
                    minOrderAmount: 50,
                    minOrderCount: 2,
                    deliveryTime: 45,
                    status: "0",
                    deliveryRange: {
                        type: "polygon",
                        points: [
                            [
                                [130.17472, 47.218971],
                                [130.17472, 47.418971],
                                [130.37472, 47.418971],
                                [130.37472, 47.218971],//黑龙江
                            ],
                        ],
                    },
                },
            ],
            page: {
                total: 0,
                currentPage: 1,
                pageSize: 20,
            },
            tableLoading: false,
            searchName: "",
            status: "",
            dialogVisible: false,
            dialogType: "add",
            editData: {},
            selectionIds: [],
        };
    },
    methods: {
        handleSelectionChange(val) {
            this.selectionIds = val.map((item) => item.id);
        },
        handleAdd() {
            this.dialogType = "add";
            this.editData = {};
            this.dialogVisible = true;
        },
        handleEdit(row) {
            this.dialogType = "edit";
            this.editData = { ...row };
            this.dialogVisible = true;
        },
        handleBatchDelete() {
            if (this.selectionIds.length === 0) {
                this.$message.warning("请选择要删除的数据");
                return;
            }
            this.$confirm("确认删除选中的运费模板吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    // TODO: 调用批量删除接口
                    this.$message.success("删除成功");
                    this.getList();
                })
                .catch(() => {});
        },
        handleDelete(row) {
            this.$confirm("确认删除该运费模板吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    // TODO: 调用删除接口
                    this.$message.success("删除成功");
                    this.getList();
                })
                .catch(() => {});
        },
        handleSearch() {
            this.page.currentPage = 1;
            this.getList();
        },
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.page.currentPage = val;
            this.getList();
        },
        handleDialogSubmit({ type, data }) {
            // TODO: 调用添加/编辑接口
            this.$message.success(`${type === "add" ? "添加" : "编辑"}成功`);
            this.dialogVisible = false;
            this.getList();
        },
        getList() {
            this.tableLoading = true;
            // TODO: 调用获取列表接口
            setTimeout(() => {
                this.page.total = 100;
                this.tableLoading = false;
            }, 500);
        },
    },
};
</script>

<style lang="less" scoped>
.freight-template-container {
    background-color: #fff;
    margin-top: 10px;
    padding-top: 10px;
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            display: flex;
            align-items: center;
            span {
                margin-right: 15px;
            }
        }
        .right {
            display: flex;
            align-items: center;
            > div {
                margin-left: 30px;
            }
            span {
                margin-right: 15px;
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }
}
</style>
