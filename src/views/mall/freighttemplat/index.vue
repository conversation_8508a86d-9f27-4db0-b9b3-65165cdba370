<template>
    <div class="execution">
        <basic-container>
            <avue-crud
                ref="crud"
                :page.sync="page"
                :data="tableData"
                :permission="permissionList"
                :table-loading="tableLoading"
                :option="tableOption"
                v-model="form"
                @on-load="getPage"
                @refresh-change="refreshChange"
                @row-update="handleUpdate"
                @row-save="handleSave"
                @row-del="handleDel"
                @sort-change="sortChange"
                @search-change="searchChange"
            >
            <template #menuLeft>
                <el-button type="primary"
                    icon="el-icon-plus"
                    size="small"
                    @click="addRow">新增
                </el-button>
            </template>
                <!-- 自定义菜单 -->
                <template #menu="{ size, row, index }">
                    <el-button
                        @click="rowView(row, index)"
                        icon="el-icon-view"
                        text
                        type="text"
                        :size="size"
                    >
                        查看
                    </el-button>
                    <el-button
                        @click="rowEdit(row, index)"
                        icon="el-icon-edit"
                        text
                        type="text"
                        :size="size"
                    >
                        编辑
                    </el-button>
                    <el-button
                        @click="handleDel(row, index)"
                        icon="el-icon-delete"
                        text
                        type="text"
                        :size="size"
                    >
                        删除
                    </el-button>
                </template>

                <template #freightTemplateForm="row">
                    <div style="width: 100%">
                        <freight-template-form
                            :row="row"
                            :edit-data="editData"
                            :dialog-type="row.type"
                            ref="freightTemplateForm"
                        ></freight-template-form>
                    </div>
                </template>
            </avue-crud>
        </basic-container>
    </div>
</template>

<script>
import {
    getPage,
    getObj,
    addObj,
    putObj,
    delObj,
} from "@/api/mall/freighttemplat";
import { tableOption } from "@/const/crud/mall/freighttemplat";
import { mapGetters } from "vuex";
import BaseQueryInfo from "@/components/BaseQueryInfo/BaseQueryInfo.vue";
import BaseTable from "@/components/BaseTable/BaseTable.vue";
import BaseFormBox from "@/components/BaseFormBox/BaseFormBox.vue";
import FreightTemplateForm from "./components/FreightTemplateForm.vue";
import { convertArrayToString } from "@/util";
export default {
    name: "freighttemplat",
    components: { BaseTable, BaseQueryInfo, BaseFormBox, FreightTemplateForm },
    data() {
        return {
            form: {},
            tableData: [],
            page: {
                total: 0, // 总页数
                currentPage: 1, // 当前页数
                pageSize: 20, // 每页显示多少条
                ascs: "sort", //升序字段
                descs: [], //降序字段
            },
            paramsSearch: {},
            tableLoading: false,
            tableOption: tableOption,
            editData: null,
        };
    },
    watch: {
        "form.type"() {
            let column1 = this.tableOption.column[4];
            let column5 = this.tableOption.column[5];
            let column2 = this.tableOption.column[6];
            let column3 = this.tableOption.column[7];
            let column4 = this.tableOption.column[8];
            let freightTemplateFormCoulumn = this.tableOption.column.filter(
                (item) => item.prop === "freightTemplate"
            )[0];
            const clearColumn = () => {
                column1.display = false;
                column2.display = false;
                column3.display = false;
                column4.display = false;
                column5.display = false;
            };
            if (this.form.type === "1") {
                // 买家承担运费
                this.tableOption.dialogCustomClass = 'freight-template-dialog';
                column1.display = true;
                freightTemplateFormCoulumn.display = false;
                if (this.form.chargeType) {
                    column5.display = true;
                    column2.display = true;
                    column3.display = true;
                    column4.display = true;
                }
            }
            if (!this.form.type || this.form.type === "2") {
                // 卖家包邮
                this.tableOption.dialogCustomClass = 'freight-template-dialog';
                clearColumn();
                freightTemplateFormCoulumn.display = false;
            }
            if (this.form.type === "3") {
                // 同城配送
                this.tableOption.dialogCustomClass = 'freight-template-dialog-add';
                clearColumn();
                freightTemplateFormCoulumn.display = true;
                this.$nextTick(() => {
                    this.$refs.freightTemplateForm.initMap();
                    this.tableOption.column = this.tableOption.column.map(
                        (item) => {
                            if (item.prop === "freightTemplate") {
                                item.rules = [
                                    {
                                        required: false,
                                        validator: async (
                                            rule,
                                            value,
                                            callback
                                        ) => {
                                            try {
                                                await this.$refs.freightTemplateForm.handleSubmit();
                                                callback();
                                            } catch (error) {
                                                console.log(error, "error");
                                                callback(
                                                    new Error(
                                                        "请完善同城配送表单"
                                                    )
                                                );
                                            }
                                        },
                                        trigger: "blur",
                                    },
                                ];
                            }
                            return item;
                        }
                    );
                });
            }
        },
        "form.chargeType"() {
            let column1 = this.tableOption.column[5];
            let column2 = this.tableOption.column[6];
            let column3 = this.tableOption.column[7];
            let column4 = this.tableOption.column[8];
            if (this.form.chargeType && this.form.type === "1") {
                column1.display = true;
                column2.display = true;
                column3.display = true;
                column4.display = true;
            } else {
                column1.display = false;
                column2.display = false;
                column3.display = false;
                column4.display = false;
            }
            if (this.form.chargeType === "1") {
                column1.label = "首件(个)";
                column3.label = "续件(个)";
                column1.precision = 0;
                column3.precision = 0;
            }
            if (this.form.chargeType === "2") {
                column1.label = "首重(kg)";
                column3.label = "续重(kg)";
                column1.precision = 2;
                column3.precision = 2;
            }
            if (this.form.chargeType === "3") {
                column1.label = "首体积(m³)";
                column3.label = "续体积(m³)";
                column1.precision = 2;
                column3.precision = 2;
            }
        },
    },
    created() {},
    mounted: function () {},
    computed: {
        ...mapGetters(["permissions"]),
        permissionList() {
            return {
                addBtn: this.permissions["mall:freighttemplat:add"]
                    ? true
                    : false,
                delBtn: this.permissions["mall:freighttemplat:del"]
                    ? true
                    : false,
                editBtn: this.permissions["mall:freighttemplat:edit"]
                    ? true
                    : false,
                viewBtn: this.permissions["mall:freighttemplat:get"]
                    ? true
                    : false,
            };
        },
    },
    methods: {
        addRow() {
            this.$refs.crud.rowAdd();
            this.editData = null
        },
        rowView(row, index) {
            console.log(row, index);
            this.tableOption.dialogCustomClass = 'freight-template-dialog-view';
            this.editData = row
            this.$refs.crud.rowView(row, index);
        },
        rowEdit (row, index) {
            this.tableOption.dialogCustomClass = 'freight-template-dialog-edit';
            console.log(row, index);
            this.editData = row
            this.$refs.crud.rowEdit(row, index);
        },
        rowDel(row, index) {
            console.log(row, index);
            this.$refs.crud.rowDel(row, index);
        },
        searchChange(params, done) {
            params = this.filterForm(params);
            this.paramsSearch = params;
            this.page.currentPage = 1;
            this.getPage(this.page, params);
            done();
        },
        sortChange(val) {
            let prop = val.prop
                ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase()
                : "";
            if (val.order == "ascending") {
                this.page.descs = [];
                this.page.ascs = prop;
            } else if (val.order == "descending") {
                this.page.ascs = [];
                this.page.descs = prop;
            } else {
                this.page.ascs = [];
                this.page.descs = [];
            }
            this.getPage(this.page);
        },
        getPage(page, params) {
            this.tableLoading = true;
            getPage(
                Object.assign(
                    {
                        current: page.currentPage,
                        size: page.pageSize,
                        descs: this.page.descs,
                        ascs: this.page.ascs,
                    },
                    params,
                    this.paramsSearch
                )
            )
                .then((response) => {
                    this.tableData = response.data.data.records;
                    this.page.total = response.data.data.total;
                    this.page.currentPage = page.currentPage;
                    this.page.pageSize = page.pageSize;
                    this.tableLoading = false;
                })
                .catch(() => {
                    this.tableLoading = false;
                });
        },
        /**
         * @title 数据删除
         * @param row 为当前的数据
         * @param index 为当前删除数据的行数
         *
         **/
        handleDel: function (row, index) {
            var _this = this;
            this.$confirm("是否确认删除此数据", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(function () {
                    return delObj(row.id);
                })
                .then((data) => {
                    _this.$message({
                        showClose: true,
                        message: "删除成功",
                        type: "success",
                    });
                    this.getPage(this.page);
                })
                .catch(function (err) {});
        },
        /**
         * @title 数据更新
         * @param row 为当前的数据
         * @param index 为当前更新数据的行数
         * @param done 为表单关闭函数
         *
         **/
        handleUpdate: async function (row, index, done, loading) {
            let transData = row;
            if (this.form.type === "3") {
                const sumitData =
                    await this.$refs.freightTemplateForm.handleSubmit();
                console.log(sumitData, "sumitData");
                console.log(row, "row");
                transData = {
                    ...row,
                    intraCityDelivery: {
                        centerLng: sumitData.data.deliveryRange.center[0], //中心点经度
                        centerLat: sumitData.data.deliveryRange.center[1], //中心点纬度
                        baseDistance: sumitData.data.deliveryFee.baseDistance, //基础距离
                        basePrice: sumitData.data.deliveryFee.basePrice, //基础价格
                        extraPerDistance:
                            sumitData.data.deliveryFee.extraPerDistance, //每超出公里
                        extraPerPrice: sumitData.data.deliveryFee.extraPerPrice, //每超出公里价格
                        scopeDelivery: sumitData.data.deliveryRange.points
                            ? convertArrayToString(
                                  sumitData.data.deliveryRange.points
                              )
                            : "", //多边形顶点坐标
                        deliveryTimesList: sumitData.data.deliveryTimeEnabled
                            ? sumitData.data.deliveryTimeList.map((item) => ({
                                  day: item.day,
                                  hour1: item.startHour,
                                  minute1: item.startMinute,
                                  hour2: item.endHour,
                                  minute2: item.endMinute,
                              }))
                            : [], //配送时间设置
                        psprehour: sumitData.data.deliveryTimeCondition, //提前下单时间
                        // radius: sumitData.data.deliveryRange.radius,//配送范围-半径
                        // status: sumitData.data.status,//1:启用 0:禁用
                    },
                };
            }
            console.log(transData, "transData");

            putObj(transData)
                .then((data) => {
                    this.$message({
                        showClose: true,
                        message: "修改成功",
                        type: "success",
                    });
                    done();
                    this.getPage(this.page);
                })
                .catch(() => {
                    loading();
                });
        },
        /**
         * @title 数据添加
         * @param row 为当前的数据
         * @param done 为表单关闭函数
         *
         **/
        handleSave: async function (row, done, loading) {
            let transData = row;
            if (this.form.type === "3") {
                const sumitData =
                    await this.$refs.freightTemplateForm.handleSubmit();
                console.log(sumitData, "sumitData");
                console.log(row, "row");
                transData = {
                    ...row,
                    intraCityDelivery: {
                        centerLng: sumitData.data.deliveryRange.center[0], //中心点经度
                        centerLat: sumitData.data.deliveryRange.center[1], //中心点纬度
                        baseDistance: sumitData.data.deliveryFee.baseDistance, //基础距离
                        basePrice: sumitData.data.deliveryFee.basePrice, //基础价格
                        extraPerDistance:
                            sumitData.data.deliveryFee.extraPerDistance, //每超出公里
                        extraPerPrice: sumitData.data.deliveryFee.extraPerPrice, //每超出公里价格
                        scopeDelivery: sumitData.data.deliveryRange.points
                            ? convertArrayToString(
                                  sumitData.data.deliveryRange.points
                              )
                            : "", //多边形顶点坐标
                        deliveryTimesList: sumitData.data.deliveryTimeEnabled
                            ? sumitData.data.deliveryTimeList.map((item) => ({
                                  day: item.day,
                                  hour1: item.startHour,
                                  minute1: item.startMinute,
                                  hour2: item.endHour,
                                  minute2: item.endMinute,
                              }))
                            : [], //配送时间设置
                        psprehour: sumitData.data.deliveryTimeCondition, //提前下单时间
                        // radius: sumitData.data.deliveryRange.radius,//配送范围-半径
                        // status: sumitData.data.status,//1:启用 0:禁用
                    },
                };
            }
            console.log(transData, "成功transData");
            // return
            addObj(transData)
                .then((data) => {
                    this.$message({
                        showClose: true,
                        message: "添加成功",
                        type: "success",
                    });
                    done();
                    this.getPage(this.page);
                })
                .catch(() => {
                    loading();
                });
        },
        /**
         * 刷新回调
         */
        refreshChange(page) {
            this.getPage(this.page);
        },
    },
};
</script>

<style lang="scss" scoped>
.freight-template-con {
    padding: 20px;
}
</style>
