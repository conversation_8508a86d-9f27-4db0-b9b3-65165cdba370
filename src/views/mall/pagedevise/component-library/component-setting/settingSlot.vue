<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 装修组件设置的 组件 -->
<template>
  <div class='settingSlot'>
    <el-container>
      <el-header height="40px" class="settingTitle">
        <div class="title">
          <slot name="dialogTitle">标题</slot>
        </div>
      </el-header>
      <el-main style="padding:5px 20px;overflow:auto;height: 630px">
        <div>
          <!-- 描述说明 -->
          <div v-if="$slots.hint" class="hintTxt">
            <slot name="hint"></slot>
          </div>
          <div class="form-item-title">内容设置</div>
          <div class="main-content">
            <slot name="mainContent"></slot>
          </div>
        </div>
      </el-main>
      <el-footer height="40px">
        <div class="settingFooterBtns">
          <slot name="btns">
            <el-button v-show="showReset" size="mini" plain type="primary" @click="$emit('reset')">重 置</el-button>
          </slot>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
export default {
  props: {
    showReset: {
      type: Boolean,
      default: false
    }
  }
}
</script>
<style lang='less' scoped>
.settingSlot {
  padding: 20px 0;
  width: 420px;
  height: 100%;
  overflow: auto;
  position: relative;
  display: flex;
  box-shadow: -5px 5px 5px #efefef;
  //border: 1px solid #efefef;

  .settingTitle {
    border-bottom: 1px solid #E4E7ED;
  }

  .title {
    font-size: 16px;
    color: #303133;
    font-weight: 500;

    .icon {
      cursor: pointer;

      &:hover {
        color: #165dff;
      }
    }
  }

  .hintTxt {
    font-size: 14px;
    color: #606266;
    margin-top: 5px;
    font-weight: 300;
  }

  .content {
    padding-left: 15px;
    padding-right: 15px;
    overflow: auto;
    position: relative;
    height: 100%;


    .main-content {
      height: 698px;
      overflow-y: auto;
    }

    &::-webkit-scrollbar { /*滚动条整体样式*/
      width: 4px; /*高宽分别对应横竖滚动条的尺寸*/
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 2px;
      background-color: #d6d6d6;
    }

  }

  .settingFooterBtns {
    text-align: right;
    background: #ffffff;
    width: 100%;
    height: 100%;
    margin-top: 10px;
  }

  //表单样式统一
  /deep/ .el-form-item {
    margin-bottom: 8px;
  }

  /deep/ .el-form-item__label {
    line-height: 30px;
  }

  /deep/ .el-form-item__content {
    line-height: 30px;
  }

  /deep/ .form-item-title {
    font-weight: 500;
    margin: 8px 0;
  }

  /* 下面为装修组件设置的通用class */
  /* 动态组件item */

  /deep/ .draggable-item {
    margin-top: 12px;
    padding: 5px 5px 5px 0px;
    background: #ffffff;
    border: 1px dashed #e5e5e5;
    position: relative;


    // 排序拖动icon
    .draggable-focus {
      font-size: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    // 删除icon
    .del-focus {
      font-size: 16px;
    }

    // 删除icon
    .draggable-del {
      background: #999;
      color: #ffffff;
      position: absolute;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      font-size: 12px;
      font-style: normal;
      line-height: 16px;
      text-align: center;
      right: -12px;
      top: -12px;
      cursor: pointer;
      z-index: 100;
    }

    .draggable-del:hover {
      background: #666;
    }
  }
}
</style>
