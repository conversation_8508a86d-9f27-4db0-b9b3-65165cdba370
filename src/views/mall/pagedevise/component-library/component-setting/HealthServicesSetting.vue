<template>
    <div class="HealthServicesSetting">
        <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
            <p slot="dialogTitle">健康服务</p>
            <div slot="hint">
            </div>
            <div slot="mainContent">
                <el-form ref="form" label-width="90px" :model="formData">
                    <el-form-item label="标题文字">
                        <el-input v-model="formData.title" size="mini" placeholder="标题文字"></el-input>
                    </el-form-item>
                    <!-- 类型：内链还是外链默认外链 -->
                    <el-form-item label="类型">
                        <el-radio-group v-model="typeStatus" size="mini">
                            <el-radio-button label="1">外链</el-radio-button>
                            <el-radio-button label="2">内链</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                    <div class="tips-class">图片尺寸: 建议为120px*120px，支持jpg、png、gif等格式.</div>
                    <el-button type="primary" plain @click="addDataInfo">添加</el-button>
                    <draggable v-model="formData.dataCon"
                               :options="{animation:500, filter:'.notDraggable', preventOnFilter: false}">
                        <div v-for="(item,index) in formData.dataCon" :key="index" class="draggable-item">
                            <el-row class="flex align-center">
                                <div class="type-status">{{ item.typeStatus == '2' ? '内链' : '外链' }}</div>
                                <el-col :span="3" style="text-align: center;" class="canDraggable">
                                    <div class="draggable-focus"><i class="el-icon-rank"></i></div>
                                </el-col>
                                <el-col :span="21">
                                    <div class="w100 df flr jc-fs alc">
                                        <MaterialList
                                            :value="item.imageUrl?[item.imageUrl]:[]"
                                            @sureSuccess="item.imageUrl = $event?$event[0]:''"
                                            @deleteMaterial="item.imageUrl = ''"
                                            type="image" shopId="-1" :num=1 :width="100" :height="100"></MaterialList>
                                        <div class="enter-show w100 df flc jc-fs als p-l-10 p-r-10 f1">
                                            <el-input v-model="item.dataName" size="mini" placeholder="请输入名称"
                                                      class="w100 m-b-10"></el-input>
                                            <el-input v-if="item.typeStatus != '2'" v-model="item.urlText" size="mini" placeholder="请输入appid"
                                                      class="w100"></el-input>
                                            <app-page-select isAutoWidth v-if="item.typeStatus == '2'" :clientType="clientType" :page="item.pathText"
                                            @change="onChangePage(item, $event)"></app-page-select>
                                            <el-input v-if="item.typeStatus != '2'" v-model="item.pathText" style="margin-top: 10px;" size="mini" placeholder="请输入路径地址"
                                                      class="w100"></el-input>
                                        </div>
                                        <el-button type="danger" size="mini" plain @click="delDataInfo(index)"
                                                   v-show="formData.dataCon.length > 4">删除
                                        </el-button>
                                        <el-button style="opacity: 0;cursor: default;" type="primary" size="mini" plain v-show="formData.dataCon.length <= 4"></el-button>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </draggable>
                </el-form>
            </div>
        </settingSlot>
    </div>
</template>

<script name="HealthServicesSetting">
import settingSlot from "./settingSlot.vue";
import iconSelect from "../pages/page-components/iconSelect.vue";
import {mapState} from "vuex";
import {deepCopy, isEmptyObj} from "@/util/otherUtils";
import AppPageSelect from "@/components/app-page-select/Index.vue";
import MaterialList from "@/components/material/list.vue";
import draggable from "vuedraggable";


export default {
    components: {draggable, MaterialList, AppPageSelect, iconSelect, settingSlot},
    data() {
        return {
            clientType: 'MA',
            formData: {},
            typeStatus:'1',//1为外链，2为内链
            formDataCopy: {
                title: '健康服务',
                dataCon: [
                    {
                        imageUrl: "",
                        dataName: "互联网医院",
                        urlText: "",
                        pathText: ""
                    },
                    {
                        imageUrl: "",
                        dataName: "挂号服务",
                        urlText: "",
                        pathText: ""
                    },
                    {
                        imageUrl: "",
                        dataName: "慢病管理",
                        urlText: "",
                        pathText: ""
                    },
                    {
                        imageUrl: "",
                        dataName: "医院直通车",
                        urlText: "",
                        pathText: ""
                    }
                ]
            },
        }
    },
    props: {
        showData: {
            type: Object,
            default: () => {
            }
        },
        config: {
            type: Object,
            default: () => {
            }
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
            clickComIndex: state => state.divpage.clickComIndex,
        })
    },
    mounted() {
        this.setShowData()
    },
    methods: {
        // 删除数据信息
        delDataInfo(index) {
            let formDataCopy = deepCopy(this.formData)
            formDataCopy['dataCon'].splice(index, 1)
            this.formData = formDataCopy
            this.$set(this.componentsList[this.clickComIndex], 'data', this.formData)
        },
        // 添加数据信息
        addDataInfo() {
            this.formData.dataCon.push({
                imageUrl: "",
                dataName: "",
                urlText: "",
                typeStatus: this.typeStatus
            })
        },
        // 设置展示数据
        setShowData() {
            let that = this;
            if (isEmptyObj(that.showData)) {
                that.formData = that.showData
            } else {
                that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
            }
            that.$set(that.componentsList[that.clickComIndex], 'data', that.formData)
        },
        cancel() {
            this.$emit('cancel')
        },
        reset() {
            let that = this;
            that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
            that.componentsList[that.clickComIndex]['data'] = that.formData;
        },
        confirm() {
            this.$emit('confirm', this.formData)
        },
        onChangePage(item,val) {
            console.log(val, item, 'val')
            item.pathText = val
        }
    }
}
</script>

<style lang="scss" scoped>
.HealthServicesSetting {
.type-status {
    position: absolute;
    right: 0;
    top: 0;
    background-color: #f00;
    color: #fff;
    padding: 0 5px;
    border-radius: 5px;
    z-index: 99;
}

}
</style>
