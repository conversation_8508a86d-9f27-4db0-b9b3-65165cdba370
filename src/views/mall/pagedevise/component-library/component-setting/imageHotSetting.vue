<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <div class="compSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">热图</p>
      <div slot="hint">
        <div>提示:可以划区进行热图设置，设置后用户点击该区域会触发事件.</div>
      </div>
      <div slot="mainContent">
        <el-form ref="form" label-position="left" :model="formData">
          <el-form-item
            label-width="90px"
            label="显示高度"
          >
            <el-input
              v-model="formData.height"
              size="mini"
              type="number"
              placeholder="高度"
            >
              <template v-slot:append>px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="背景颜色" label-width="90px">
            <bg-color-select :thememobile="thememobile" :bgValue="formData.background"
                             @onChange="formData.background = $event"></bg-color-select>
          </el-form-item>
          <el-form-item
            label-width="90px"
            label="图片"
          >
            <material-list
              :value="formData.imageUrl ? [formData.imageUrl] : []"
              type="image"
              @sureSuccess="formData.imageUrl = $event ? $event[0] : ''"
              @deleteMaterial="formData.imageUrl = ''"
              :divStyle="'width:100%;margin-bottom:0px;height:100px;line-height: 100px;'"
              :num="1"
            />
          </el-form-item>
          <el-form-item label="跳转链接"/>
          <el-button
            size="small"
            :disabled="!formData.imageUrl"
            type="primary"
            icon="el-icon-circle-plus"
            plain
            @click="addHotspots"
          >添加热区
          </el-button
          >
          <el-button
            size="small"
            :disabled="!formData.imageUrl"
            type="primary"
            icon="edit"
            plain
            @click="editHotspots"
          >编辑热区
          </el-button
          >
          <div>
            <div
              v-for="(item, index) in formData.hotspotsPosition"
              :key="index"
              class="draggable-item"
              style="padding-left: 5px !important"
            >
              <i class="draggable-del" @click="delHotspots(index)">x</i>
              <div style="margin: 5px 0">
                热区{{ index + 1 }}
              </div>
              <div>
                <app-page-select :is-refresh="true" :clientType="clientType" :page="item.pageUrl"
                                 @change="item.pageUrl=$event"></app-page-select>
              </div>
            </div>
          </div>
          <div class="form-item-title">图片圆角</div>
          <el-row :gutter="20" style="padding-left: 10px">
            <el-col :span="12">
              <el-form-item label="左上">
                <el-input-number
                  v-model="formData.topLeftRadius"
                  size="small"
                  type="number"
                  placeholder="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="右上">
                <el-input-number
                  v-model="formData.topRightRadius"
                  size="small"
                  type="number"
                  placeholder="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="左下">
                <el-input-number
                  v-model="formData.bottomLeftRadius"
                  size="small"
                  type="number"
                  placeholder="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="右下">
                <el-input-number
                  v-model="formData.bottomRightRadius"
                  size="small"
                  type="number"
                  placeholder="0"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </settingSlot>
    <!-- 热图编辑弹框 -->
    <el-dialog :visible.sync="editDialog" width="435px" :close-on-click-modal="false" :append-to-body="true"
               title="热区编辑" top="20px">
      <div style="position: relative;">
        <div style="width: 100%;height:100%;border: 1px solid #efefef">
          <PdImageHot :setData="formData"/>
        </div>
        <div style="margin-top: 30px;text-align:right;">
          <el-button
            size="mini"
            :disabled="!formData.imageUrl"
            type="primary"
            icon="el-icon-circle-plus"
            plain
            @click="addHotspots"
          >添加热区
          </el-button>
          <el-button
            size="mini"
            icon="el-icon-close"
            plain
            @click="editDialog=false"
          >关闭
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

import settingSlot from './settingSlot'
import MaterialList from '@/components/material/list.vue'
import AppPageSelect from '@/components/app-page-select/Index.vue'
import bgColorSelect from "@/views/mall/pagedevise/component-library/pages/page-components/bgColorSelect.vue";
import PdImageHot from "@/views/mall/pagedevise/component-library/components/imageHot.vue";

export default {
  components: {bgColorSelect, settingSlot, MaterialList, AppPageSelect, PdImageHot},
  props: {
    thememobile: {type: Object | Array},
    clientType: [String],
    showData: {
      type: Object,
      default: () => {
      }
    },
    config: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      editDialog: false,
      formDataCopy: {
        styles: {},
        background: "#FFFFFF",
        hotspotsPosition: [],
        height: 200,
        imageUrl: "",
        pageUrl: "",
        pageSpacing: 0
      },
      formData: {
        pageUrl: ''
      }
    };
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    clientType() {
    }
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该组件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    addHotspots() {
      this.formData.hotspotsPosition.push({
        boxHeight: 100,
        boxWidth: 200,
        boxTLPoint: {x: 0, y: 0},
        linkUrl: ""
      });
    },
    delHotspots(index) {
      this.formData.hotspotsPosition.splice(index, 1);
    },
    editHotspots() {
      this.editDialog = true;
    }
  }
};
</script>
<style lang='less' scoped>
.compSetting {

}
</style>
