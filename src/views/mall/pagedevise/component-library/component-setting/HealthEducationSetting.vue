<template>
    <div class="HealthEducationSetting">
        <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
            <p slot="dialogTitle">健康宣教</p>
            <div slot="hint">
            </div>
            <div slot="mainContent">
                <el-form ref="form" label-width="90px" :model="formData">
                    <el-form-item label="标题文字">
                        <el-input v-model="formData.title" size="mini" placeholder="标题文字"></el-input>
                    </el-form-item>
                    <div class="tips-class">图片尺寸: 建议为120px*120px，支持jpg、png、gif等格式.</div>
                    <el-button type="primary" plain @click="addDataInfo">添加</el-button>
                    <draggable v-model="formData.dataCon"
                               :options="{animation:500, filter:'.notDraggable', preventOnFilter: false}">
                        <div v-for="(item,index) in formData.dataCon" :key="index" class="draggable-item">
                            <el-row class="flex align-center">
                                <el-col :span="3" style="text-align: center;" class="canDraggable">
                                    <div class="draggable-focus"><i class="el-icon-rank"></i></div>
                                </el-col>
                                <el-col :span="21">
                                    <div class="w100 df flr jc-fs alc">
                                        <MaterialList
                                            :value="item.imageUrl?[item.imageUrl]:[]"
                                            @sureSuccess="item.imageUrl = $event?$event[0]:''"
                                            @deleteMaterial="item.imageUrl = ''"
                                            type="image" shopId="-1" :num=1 :width="100" :height="100"></MaterialList>
                                        <div class="enter-show w100 df flc jc-fs als p-l-10 p-r-10 f1">
                                            <el-input v-model="item.dataName" size="mini" placeholder="名称"
                                                      class="w100 m-b-10"></el-input>
                                            <el-input v-model="item.urlText" size="mini" placeholder="url链接"
                                                      class="w100"></el-input>
                                        </div>
                                        <el-button type="danger" size="mini" plain @click="delDataInfo(index)"
                                                   v-show="formData.dataCon.length > 4">删除
                                        </el-button>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </draggable>
                </el-form>
            </div>
        </settingSlot>
    </div>
</template>

<script name="HealthEducationSetting">
import settingSlot from "./settingSlot.vue";
import iconSelect from "../pages/page-components/iconSelect.vue";
import {mapState} from "vuex";
import {deepCopy, isEmptyObj} from "@/util/otherUtils";
import AppPageSelect from "@/components/app-page-select/Index.vue";
import MaterialList from "@/components/material/list.vue";
import draggable from "vuedraggable";

export default {
    components: {draggable, MaterialList, AppPageSelect, iconSelect, settingSlot},
    data() {
        return {
            formData: {},
            formDataCopy: {
                title: '健康服务',
                dataCon: [
                    {
                        imageUrl: "",
                        dataName: "互联网医院",
                        urlText: ""
                    },
                    {
                        imageUrl: "",
                        dataName: "挂号服务",
                        urlText: ""
                    },
                    {
                        imageUrl: "",
                        dataName: "慢病管理",
                        urlText: ""
                    },
                    {
                        imageUrl: "",
                        dataName: "医院直通车",
                        urlText: ""
                    }
                ]
            },
        }
    },
    props: {
        showData: {
            type: Object,
            default: () => {
            }
        },
        config: {
            type: Object,
            default: () => {
            }
        }
    },
    computed: {
        ...mapState({
            componentsList: state => state.divpage.componentsList,
            clickComIndex: state => state.divpage.clickComIndex,
        })
    },
    mounted() {
        this.setShowData()
    },
    methods: {
        // 删除数据信息
        delDataInfo(index) {
            let formDataCopy = deepCopy(this.formData)
            formDataCopy['dataCon'].splice(index, 1)
            this.formData = formDataCopy
            this.$set(this.componentsList[this.clickComIndex], 'data', this.formData)
        },
        // 添加数据信息
        addDataInfo() {
            this.formData.dataCon.push({
                imageUrl: "",
                dataName: "",
                urlText: ""
            })
        },
        // 设置展示数据
        setShowData() {
            let that = this;
            if (isEmptyObj(that.showData)) {
                that.formData = that.showData
            } else {
                that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
            }
            that.$set(that.componentsList[that.clickComIndex], 'data', that.formData)
        },
        cancel() {
            this.$emit('cancel')
        },
        reset() {
            let that = this;
            that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
            that.componentsList[that.clickComIndex]['data'] = that.formData;
        },
        confirm() {
            this.$emit('confirm', this.formData)
        },
    }
}
</script>

<style lang="scss" scoped>
.HealthEducationSetting {

}
</style>
