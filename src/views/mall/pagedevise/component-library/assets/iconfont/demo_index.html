<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://gtms04.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>

      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1203543" target="_blank" class="nav-more">查看项目</a>

    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">

            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">购物车</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe789;</span>
                <div class="name">reload time</div>
                <div class="code-name">&amp;#xe789;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe78c;</span>
                <div class="name">poweroff</div>
                <div class="code-name">&amp;#xe78c;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe78e;</span>
                <div class="name">setting</div>
                <div class="code-name">&amp;#xe78e;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe792;</span>
                <div class="name">app store</div>
                <div class="code-name">&amp;#xe792;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">user</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7af;</span>
                <div class="name">line chart</div>
                <div class="code-name">&amp;#xe7af;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7b0;</span>
                <div class="name">bar chart</div>
                <div class="code-name">&amp;#xe7b0;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7cb;</span>
                <div class="name">flag</div>
                <div class="code-name">&amp;#xe7cb;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7cd;</span>
                <div class="name">money collect</div>
                <div class="code-name">&amp;#xe7cd;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">shop</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7cf;</span>
                <div class="name">shopping</div>
                <div class="code-name">&amp;#xe7cf;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">gateway</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7dd;</span>
                <div class="name">qrcode</div>
                <div class="code-name">&amp;#xe7dd;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7e2;</span>
                <div class="name">link</div>
                <div class="code-name">&amp;#xe7e2;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe7e4;</span>
                <div class="name">tag</div>
                <div class="code-name">&amp;#xe7e4;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe860;</span>
                <div class="name">image-fill</div>
                <div class="code-name">&amp;#xe860;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe8ef;</span>
                <div class="name">search</div>
                <div class="code-name">&amp;#xe8ef;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">service</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">query</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe614;</span>
                <div class="name">query_off</div>
                <div class="code-name">&amp;#xe614;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">feedback</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">avatar</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">arrow</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">页面设置</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">拖拽激活</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">设置-填充</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">上移箭头</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>

            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">下移箭头</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>

          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">

          <li class="dib">
            <span class="icon iconfont icon-gouwuche"></span>
            <div class="name">
              购物车
            </div>
            <div class="code-name">.icon-gouwuche
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-reloadtime"></span>
            <div class="name">
              reload time
            </div>
            <div class="code-name">.icon-reloadtime
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-poweroff"></span>
            <div class="name">
              poweroff
            </div>
            <div class="code-name">.icon-poweroff
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-setting"></span>
            <div class="name">
              setting
            </div>
            <div class="code-name">.icon-setting
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-appstore"></span>
            <div class="name">
              app store
            </div>
            <div class="code-name">.icon-appstore
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-user"></span>
            <div class="name">
              user
            </div>
            <div class="code-name">.icon-user
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-linechart"></span>
            <div class="name">
              line chart
            </div>
            <div class="code-name">.icon-linechart
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-barchart"></span>
            <div class="name">
              bar chart
            </div>
            <div class="code-name">.icon-barchart
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-flag"></span>
            <div class="name">
              flag
            </div>
            <div class="code-name">.icon-flag
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-moneycollect"></span>
            <div class="name">
              money collect
            </div>
            <div class="code-name">.icon-moneycollect
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shop"></span>
            <div class="name">
              shop
            </div>
            <div class="code-name">.icon-shop
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shopping"></span>
            <div class="name">
              shopping
            </div>
            <div class="code-name">.icon-shopping
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-gateway"></span>
            <div class="name">
              gateway
            </div>
            <div class="code-name">.icon-gateway
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-qrcode"></span>
            <div class="name">
              qrcode
            </div>
            <div class="code-name">.icon-qrcode
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-link"></span>
            <div class="name">
              link
            </div>
            <div class="code-name">.icon-link
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-tag"></span>
            <div class="name">
              tag
            </div>
            <div class="code-name">.icon-tag
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-image-fill"></span>
            <div class="name">
              image-fill
            </div>
            <div class="code-name">.icon-image-fill
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-search"></span>
            <div class="name">
              search
            </div>
            <div class="code-name">.icon-search
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-service"></span>
            <div class="name">
              service
            </div>
            <div class="code-name">.icon-service
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-query"></span>
            <div class="name">
              query
            </div>
            <div class="code-name">.icon-query
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-query_off"></span>
            <div class="name">
              query_off
            </div>
            <div class="code-name">.icon-query_off
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-feedback"></span>
            <div class="name">
              feedback
            </div>
            <div class="code-name">.icon-feedback
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-avatar"></span>
            <div class="name">
              avatar
            </div>
            <div class="code-name">.icon-avatar
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-arrow"></span>
            <div class="name">
              arrow
            </div>
            <div class="code-name">.icon-arrow
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-yemianshezhi"></span>
            <div class="name">
              页面设置
            </div>
            <div class="code-name">.icon-yemianshezhi
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-tuozhuaijihuo"></span>
            <div class="name">
              拖拽激活
            </div>
            <div class="code-name">.icon-tuozhuaijihuo
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shezhi-tianchong"></span>
            <div class="name">
              设置-填充
            </div>
            <div class="code-name">.icon-shezhi-tianchong
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-shangyijiantou"></span>
            <div class="name">
              上移箭头
            </div>
            <div class="code-name">.icon-shangyijiantou
            </div>
          </li>

          <li class="dib">
            <span class="icon iconfont icon-xiayijiantou"></span>
            <div class="name">
              下移箭头
            </div>
            <div class="code-name">.icon-xiayijiantou
            </div>
          </li>

        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gouwuche"></use>
                </svg>
                <div class="name">购物车</div>
                <div class="code-name">#icon-gouwuche</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-reloadtime"></use>
                </svg>
                <div class="name">reload time</div>
                <div class="code-name">#icon-reloadtime</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-poweroff"></use>
                </svg>
                <div class="name">poweroff</div>
                <div class="code-name">#icon-poweroff</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-setting"></use>
                </svg>
                <div class="name">setting</div>
                <div class="code-name">#icon-setting</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-appstore"></use>
                </svg>
                <div class="name">app store</div>
                <div class="code-name">#icon-appstore</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-user"></use>
                </svg>
                <div class="name">user</div>
                <div class="code-name">#icon-user</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-linechart"></use>
                </svg>
                <div class="name">line chart</div>
                <div class="code-name">#icon-linechart</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-barchart"></use>
                </svg>
                <div class="name">bar chart</div>
                <div class="code-name">#icon-barchart</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-flag"></use>
                </svg>
                <div class="name">flag</div>
                <div class="code-name">#icon-flag</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-moneycollect"></use>
                </svg>
                <div class="name">money collect</div>
                <div class="code-name">#icon-moneycollect</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shop"></use>
                </svg>
                <div class="name">shop</div>
                <div class="code-name">#icon-shop</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shopping"></use>
                </svg>
                <div class="name">shopping</div>
                <div class="code-name">#icon-shopping</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gateway"></use>
                </svg>
                <div class="name">gateway</div>
                <div class="code-name">#icon-gateway</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qrcode"></use>
                </svg>
                <div class="name">qrcode</div>
                <div class="code-name">#icon-qrcode</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-link"></use>
                </svg>
                <div class="name">link</div>
                <div class="code-name">#icon-link</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tag"></use>
                </svg>
                <div class="name">tag</div>
                <div class="code-name">#icon-tag</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-image-fill"></use>
                </svg>
                <div class="name">image-fill</div>
                <div class="code-name">#icon-image-fill</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-search"></use>
                </svg>
                <div class="name">search</div>
                <div class="code-name">#icon-search</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-service"></use>
                </svg>
                <div class="name">service</div>
                <div class="code-name">#icon-service</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-query"></use>
                </svg>
                <div class="name">query</div>
                <div class="code-name">#icon-query</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-query_off"></use>
                </svg>
                <div class="name">query_off</div>
                <div class="code-name">#icon-query_off</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-feedback"></use>
                </svg>
                <div class="name">feedback</div>
                <div class="code-name">#icon-feedback</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-avatar"></use>
                </svg>
                <div class="name">avatar</div>
                <div class="code-name">#icon-avatar</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-arrow"></use>
                </svg>
                <div class="name">arrow</div>
                <div class="code-name">#icon-arrow</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yemianshezhi"></use>
                </svg>
                <div class="name">页面设置</div>
                <div class="code-name">#icon-yemianshezhi</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuozhuaijihuo"></use>
                </svg>
                <div class="name">拖拽激活</div>
                <div class="code-name">#icon-tuozhuaijihuo</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shezhi-tianchong"></use>
                </svg>
                <div class="name">设置-填充</div>
                <div class="code-name">#icon-shezhi-tianchong</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shangyijiantou"></use>
                </svg>
                <div class="name">上移箭头</div>
                <div class="code-name">#icon-shangyijiantou</div>
            </li>

            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiayijiantou"></use>
                </svg>
                <div class="name">下移箭头</div>
                <div class="code-name">#icon-xiayijiantou</div>
            </li>

          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
