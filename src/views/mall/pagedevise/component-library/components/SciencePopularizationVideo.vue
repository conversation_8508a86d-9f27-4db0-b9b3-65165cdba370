<template>
    <div class="SciencePopularizationVideo w100 p-10">
      <div class="main-box w100 df flc jc-fs alc bc1">
        <div class="box-title w100 df flr jc-fs alc m-b-16">
          <div class="title-icon dfc m-r-3">
            <img class="w-18 h-15" :src="setData.titleIcon" alt="图标">
          </div>
          <div class="title-text dfc">
            <p class="fs-17 fwb fc2">{{ setData.title }}</p>
          </div>
        </div>
        <div class="box-data-show w100">
          <template v-for="(item,index) in setData.dataCon">
            <div class="box-data-item w100 dfc" :key="index">
              <img class="w100 h-70" :src="item['imageUrl']" alt="图片展示">
            </div>
          </template>
        </div>
      </div>
    </div>
</template>

<script name="SciencePopularizationVideo">
export default {
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      curDataArr: [
        {
          url: "",
          icon: "icon-3/jiankangyangsheng.png"
        },
        {
          url: "",
          icon: "icon-3/xinlifudao.png"
        },
        {
          url: "",
          icon: "icon-3/shenghuozhidao.png"
        },
        {
          url: "",
          icon: "icon-3/yixuezhishi.png"
        }
      ]
    }
  },
  mounted() {
  },
  methods: {
    setCurImg(str) {
      return require(`../assets/images/${str}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.SciencePopularizationVideo {
  background-color: #F0F0F0;
  .main-box {
    border-radius: 10px;
    padding: 14px 10px;
    .box-data-show {
      display: grid;
      grid-gap: 8px 10px;
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
</style>
