<template>
    <div class="HealthEducation w100 p-10">
      <div class="main-con-box w100 bc1">
        <div class="box-title-con w100 df flr jc-sb alc m-b-17">
          <div class="title-con-left df flr jc-fs alc">
            <div class="left-img dfc p-r-6">
              <img class="wh-15" src="../assets/images/icon-3/jiankangxuanjiao.png" alt="图标">
            </div>
            <div class="left-text dfc">
              <p class="fs-17 fwb fc2">健康宣教</p>
            </div>
          </div>
          <div class="title-con-right df flr jc-fe alc">
            <div class="right-text dfc">
              <p class="fs-13 fc3 fw-4">更多</p>
            </div>
            <div class="right-icon dfc p-l-9">
              <img class="w-5 h-9" src="" alt="图标">
            </div>
          </div>
        </div>
        <div class="show-data-list w100 df flc jc-fs alc">
          <div class="data-item w100 df flr jc-sb alc f1">
            <div class="item-img dfc p-r-10">
              <img class="wh-70" src="../assets/images/icon-3/jiankangxuanjiao.png" alt="图片">
            </div>
            <div class="item-info w100 df flc jc-fs alc">
              <div class="info-title w100 df flr jc-fs alc m-t-7 m-b-15">
                <p class="fs-15 fw-5 fc2 line2">【温馨陪伴，健康通行】——一位陪诊员的历险记一位陪诊员的历险记一位陪诊员的历险记一位陪诊员</p>
              </div>
              <div class="info-con w100 df flr jc-sb alc">
                <div class="see-con df flr jc-fs alc">
                  <div class="see-icon dfc">
                    <img class="w-15 h-10" src="../assets/images/icon-3/chakanxinxi.png" alt="查看图标">
                  </div>
                  <div class="see-text dfc p-l-6">
                    <p class="fs-14 fc4 fw-5">100</p>
                  </div>
                </div>
                <div class="time-text dfc">
                  <p class="fs-14 fw-4 fc4">2024-11-12</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script name="HealthEducation">

</script>

<style lang="scss" scoped>
.HealthEducation {
  background-color: #F0F0F0;
  .main-con-box {
    padding: 14px 10px;
    border-radius: 10px;
  }
}
</style>
