<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <div class="goodsComponent bc2" :style="{marginBottom: `${setData.pageSpacing}px`}">
    <div class="goods-title w100 dfc" style="min-height: 40px;" v-if="setData.showTitle!='gone'">
<!--      <div class="action text-bold" :style="{color: `${setData.titleColor}`}" style="font-size: 13px;">-->
<!--        <div class="cuIcon-move"></div>-->
<!--        <div style="margin: 0 5px;" :class="setData.titleIcon"></div>-->
<!--        {{ setData.title }}-->
<!--        <div class="cuIcon-move"></div>-->
<!--      </div>-->
      <div class="title-box w100 df flr jc-fs alc p-l-10">
        <div class="box-line m-r-8"></div>
        <div class="box-text dfc">
          <p class="fs-17 fwb fc2">{{ setData.title }}</p>
        </div>
      </div>
    </div>
    <div style="margin-top: -8px;">
      <div v-if="setData.showType=='row'" class="cu-card article no-card" style="padding:0 10px;margin-bottom: 10px;">
        <div class="cu-item goods-item solid-bottom" v-for="(item, index) in goodsList" :key="index"
             style="padding-bottom: 10px;">
          <div v-if="item">
            <div class="content">
              <img :src="item.picUrls[0] ? item.picUrls[0] : noPic" mode="aspectFill" class="row-img">
              <div class="desc block">
                <div class="text-black margin-top-sm overflow-2">{{ item.name }}</div>
                <div class="text-gray text-sm margin-top-sm overflow-1">{{ item.sellPoint }}</div>
                <div class="flex margin-top-sm">
                  <div class="cu-tag bg-red radius sm" v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮
                  </div>
                  <div class="cu-tag bg-orange radius sm" v-if="item.pointsGiveSwitch == '1'">积分</div>
                  <div class="text-gray text-sm padding-lr-sm">已售{{ item.saleNum }}</div>
                </div>
                <div class="flex justify-between margin-tb-sm">
                  <div class="text-price text-bold text-lg text-red">{{ item.priceDown }}</div>
                  <div class="round buy text-sm " :class="'bg-red'">
                    <div>立即购买</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else-if="setData.showType=='card'" class="goods-container flex">
        <div class="goods-box" v-for="(item, index) in goodsList" :key="index">
          <div v-if="item">
            <div class="img-box">
              <img :src="item.picUrls[0] ? item.picUrls[0] : noPic" mode="aspectFill" class="card-img">
            </div>
            <div class="text-black margin-top-xs padding-lr-sm overflow-2">{{ item.name }}</div>
            <div class="flex justify-between margin-top-sm align-center">
              <div class="text-price text-bold text-red text-lg padding-lr-sm">{{ item.priceDown }}</div>
              <div class="cu-tag bg-red radius sm" v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮
              </div>
              <div class="text-gray text-sm padding-lr-sm">已售{{ item.saleNum }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";

import {getListByIds} from '@/api/mall/goodsspu'

export default {
  data() {
    return {
      noPic: require('../assets/images/icon/<EMAIL>'),
      goodsList: [],
      initData: true
    };
  },
  components: {placeholderImg},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    }),
  },
  created() {
  },
  mounted() {
    this.getGoodsListByIds()//初次加载数据根据ids获取商品
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    getGoodsListByIds() {//根据ids获取商品
      if (this.setData.goodsIds) {
        if (this.setData.goodsIds.length > 0) {
          getListByIds(this.setData.goodsIds).then(response => {
            //如果之前的商品被删除了，那么需要自动删掉处理
            let hasGoodsIds = response.data.data.map(item => item.id)
            let goodsIds = JSON.parse(JSON.stringify(this.setData.goodsIds))
            goodsIds.map((item, index) => {
              let hasIndex = hasGoodsIds.indexOf(item);
              if (hasIndex === -1) {
                this.setData.goodsIds.splice(index, 1)
              }
            })

            let goods = new Array(this.setData.goodsIds.length)
            response.data.data.map(item => {
              let index = this.setData.goodsIds.indexOf(item.id);
              goods[index] = item
            })
            this.goodsList = goods
          })
        } else {
          this.goodsList = []
        }
      } else {
        this.goodsList = this.setData.goodsList
      }
    },
  },
  watch: {
    'setData.goodsList': {
      handler(newVal, oldVal) {
        if (this.initData) { // setData.goodsList 初次加载的数据是缓存数据，需要舍弃掉
          this.initData = false
        } else {
          this.goodsList = this.setData.goodsList || []
        }
      }
    },
    componentsList(newVal, oldVal) {          //添加的时候出发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  beforeDestroy() {
    // this.$root.Bus.$off('addHotSpot')
  }
};
</script>
<style lang='less' scoped>

@import '../colorui/main.css';
@import '../colorui/icon.css';

.goodsComponent {
  position: relative;
  display: block;
  width: 100%;
  .goods-title {
    padding: 10px 10px 0 10px;
    .title-box {
      height: 40px;
      background-color: #FFFFFF;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      .box-line {
        width: 4px;
        height: 17px;
        border-radius: 4px;
        background-color: #FF6203;
      }
    }
  }

  .goods-item {
    margin: auto !important;
    margin-top: 10px !important;
  }

  .row-img {
    margin-top: 13px;
    margin-left: -17px;
    margin-right: 10px;
    width: 120px !important;
    height: 120px !important;
    border-radius: 5px
  }

  .card-img {
    width: 100% !important;
    height: 100% !important;
    border-radius: 5px
  }

  .buy {
    padding: 3px 10px 5px 10px;
  }

  .goods-container {
    justify-content: space-between;
    flex-wrap: wrap;
    box-sizing: content-box;
    padding: 10px;
  }

  .goods-box {
    width: 170px;
    height: 265px;
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-radius: 5px;
    box-shadow: 0px 0px 30px #e5e5e5;
  }

  .goods-box .img-box {
    width: 100%;
    height: 175px;
    overflow: hidden;
  }

  .goods-box .img-box image {
    width: 100%;
    height: 265px;
  }
}

.overflow-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.overflow-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
