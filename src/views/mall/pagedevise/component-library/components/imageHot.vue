<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!--热图组件-->
<template>
  <div :style="{marginBottom: `${setData.pageSpacing}px`}">
    <div class="cp-image-hot">
      <div v-if="setData.imageUrl" ref="imageRef">
        <el-image
          :src="setData.imageUrl"
          style="width: 100%"
          fit="fill"
          :style="{
            height: `${setData.height}px`,
            borderTopLeftRadius: `${setData.topLeftRadius}px`,
            borderTopRightRadius: `${setData.topRightRadius}px`,
            borderBottomLeftRadius: `${setData.bottomLeftRadius}px`,
            borderBottomRightRadius: `${setData.bottomRightRadius}px`
          }"
          @load="loadImage"
        >
        </el-image>
      </div>
      <placeholderImg v-else></placeholderImg>
      <div v-if="!loading " style="width: 100%">
        <div
          v-for="(item, index) in hotspotsPosition"
          :key="index"
          ref="dragBoxRef"
          v-drag-move="{
            setPosition: setPosition,
            outBoxSize: {
              w: imageInfo.width,
              h: setData.height,
              index: index
            }
          }"
          class="image-hot-container"
          :style="{
            left: item.boxTLPoint.x + 'px',
            top: item.boxTLPoint.y + 'px',
            height: item.boxHeight + 'px',
            width: item.boxWidth + 'px'
          }"
        >
          {{ index + 1 }}
          <div
            id="image-hot-set"
            v-drag-eagle="{
              setPosition: setPosition,
              outBoxSize: {
                w: imageInfo.width,
                h: setData.height,
                index: index
              }
            }"
          >
            <div
              id="move-set"
              title="拖动调整大小"
            />
          </div>
          <el-button
            circle
            type="danger"
            size="mini"
            icon="el-icon-delete"
            style="position: absolute;right: 0;top:0;z-index: 10000;"
            @click="delHotspots(index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import placeholderImg from "../pages/page-components/placeholderImg";

export default {
  components: {placeholderImg},
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
    })
  },
  data() {
    return {
      loading: false,
      hotspotsPosition: this.setData.hotspotsPosition,
      imageInfo: {
        width: 393,
        height: 0
      }
    };
  },
  watch: {
    componentsList(newVal, oldVal) {          //添加的时候触发（解决没有移动的时候不emit数据）
      let that = this;
      that.updateData({componentsList: that.componentsList})
    }
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 热区
    setPosition({left = 0, top = 0, index = null} = {}) {
      const boxInfoData = this.$refs.dragBoxRef[index].getBoundingClientRect();
      const dataItem = this.setData.hotspotsPosition[index];
      dataItem.boxHeight = boxInfoData.height;
      dataItem.boxWidth = boxInfoData.width;
      dataItem.boxTLPoint = {x: left, y: top};
    },
    delHotspots(index) {
      this.setData.hotspotsPosition.splice(index, 1);
    },
    loadImage() {
      setTimeout(() => {
        const imageInfo = this.$refs.imageRef.getBoundingClientRect();
        this.imageInfo.width = imageInfo.width;
        this.imageInfo.height = imageInfo.height;
        this.hotspotsPosition = this.setData.hotspotsPosition;
        this.loading = false;
      }, 200);
    }
  },
};
</script>
<style lang='less' scoped>

.cp-image-hot {
  position: relative;

  .image-hot-container {
    position: absolute;
    z-index: 2;
    overflow: hidden;
    cursor: move;
    visibility: visible;
    background: #f1f1f1;
    opacity: 0.6;

    #image-hot-set {
      position: absolute;
      right: 1px;
      bottom: 1px;
      width: 28px;
      height: 28px;
      cursor: se-resize;

      #move-set {
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 2;
        width: 27px;
        height: 27px;
        cursor: se-resize;
        background: #ccc;
        background-position: 0 0;

        &:hover {
          background: #696969;
        }
      }
    }
  }
}
</style>
