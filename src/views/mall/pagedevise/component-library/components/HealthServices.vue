<template>
  <div class="HealthServices w100 p-10">
    <div class="main-con-box w100 df flc jc-fs alc bc1">
      <div class="health-title w100 df flr jc-fs alc m-b-20">
        <p class="fs-16 fwb fc2">{{setData.title}}</p>
      </div>
      <div class="health-con-box w100">
        <div class="swiper mySwiper">
          <div class="swiper-wrapper">
            <template v-for="(item,index) in healthServicesData">
              <div class="swiper-slide m-r-20" :key="index">
                <div class="slide-list w100 dfc">
                  <template v-for="(ite,ind) in item['data']">
                    <div class="list-item df flc jc-fs alc m-b-10" :key="ind">
                      <div class="item-img dfc">
                        <img class="wh-45" :src="ite['imageUrl']" alt="图片">
                      </div>
                      <div class="item-text dfc p-t-9">
                        <p class="fs-13 fc2 fw-5">{{ ite['dataName'] }}</p>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
          <div class="swiper-pagination m-t-16"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Swiper from 'swiper'
export default {
  props: {
    theme: {type: Object | Array},
    setData: {type: Object | Array},
    cId: {type: Number},
    noEditor: {
      type: Boolean,
      default: false,
    }
  },
  data: () => {
    return {
      healthServicesData:[]
    }
  },
  watch: {
    setData: {
      immediate: true,
      handler(newVal) {
        let data = newVal.dataCon.slice(0, 4)
        this.healthServicesData.push({data})
      }
    }
  },
  mounted() {
    this.createSwiper()
  },
  methods: {
    createSwiper() {
      let swiperCon = new Swiper('.mySwiper', {
        pagination: {
          el: ".swiper-pagination",
          type: "progressbar",
        }
      })
    },
    setCurImg(str) {
      return str?require(`../assets/images/${str}`):''
    }
  }
}
</script>

<style lang="scss" scoped>
.HealthServices {
  background-color: #F0F0F0;

  .main-con-box {
    border-radius: 10px;

    .health-title {
      padding: 13px 0 17px 15px;
    }

    .health-con-box {
      padding: 0 13px 16px 13px;
      .mySwiper {
        .swiper-wrapper {
          width: 100%;
          .swiper-slide {
            width: 100% !important;
            height: 1px;
            .slide-list {
              display: grid;
              grid-template-columns: repeat(4, 1fr);
              height:auto
            }
          }
          .swiper-slide-active {
            height: auto;
          }
        }
        .swiper-pagination {
          width: 40px;
          position: relative;
          border-radius: 10px;
          left: 45%;
          border: 1px solid #FF6203;
          ::v-deep.swiper-pagination-progressbar-fill {
            background-color: #FF6203;
          }
        }
      }
    }
  }
}
</style>
