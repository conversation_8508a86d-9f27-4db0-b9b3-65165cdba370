/*
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
.pc-div-page-index {
  background: #F0F0F0;
  // position: relative;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: auto;

  .font-weight-300 {
    font-weight: 300;
  }

  .top {
    position: fixed;
    top: 50%;
    left: 0;
    height: 40px;
    width: 30px;
    font-size: 16px;
    font-weight: 300;
    border: 1px solid #d7dae2;
    z-index: 99999;
    cursor: pointer;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    //阴影
    box-shadow: 0 0 5px #a9a9a9;
    //右边圆角
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;


    &:hover {
      color: #165dff;
      cursor: pointer;
    }
  }

  .left-div {
    background: #FFFFFF;
    position: fixed;
    top: 60px;
    left: 20px;
    border: 1px solid #d7dae2;
    padding: 8px;
    border-radius: 4px;
    width: 170px;
    z-index: 999;
  }

  .top-image-box {
    width: 100%;
    height: 80px;
  }

  .top-image {
    width: 100%;
    height: 80px;
    margin: auto;

    &:hover {
      cursor: move;
      border: 1px dashed #165dff;
    }
  }

  .top-image-edit {
    cursor: pointer;
    position: absolute;
    top: 5px;
    right: 200px;

  }

  .close-img-shade {
    cursor: pointer;
  }

  .close-img-shade::before, .close-img-shade::after {
    content: '';
    position: absolute;
    height: 2px;
    width: 20px;
    top: 20px;
    right: 240px;
    //margin-top: -1px;
    background-color: #999;
  }

  .close-img-shade::before {
    transform: rotate(45deg);
  }

  .close-img-shade::after {
    transform: rotate(-45deg);
  }

  .content {
    position: relative;
    margin-right: auto;
    margin-left: auto;
    height: 100%;

    .focus-class {
      border: 2px dashed #165dff !important;
    }

    .showContent {
      position: relative;
      margin-right: auto;
      margin-left: auto;
      overflow-y: hidden;
      overflow-x: hidden;
      height: 100%;

      .pageContent {
        position: relative;
        overflow-y: hidden;
        overflow-x: hidden;
        height: 100%;

        .pageTopBlock {
          top: 0;
          left: 0;
          width: 100%;
          height: 39px;
          position: absolute;

          .pageTopImg {
            width: 100%;
            height: 49px;
          }

          p {
            position: absolute;
            left: 0;
            bottom: 15px;
            width: 100%;
            text-align: center;
          }

        }

        .componentsList {
          margin-top: 778px;
          background: #f1f1f1;

          &::-webkit-scrollbar {
            display: none
          }
        ;
        }

        .drag-item {
          // padding: 10px;
          // width: 355px;
          margin: auto;
          position: relative;
          background: #ffffff;

          &:hover {
            cursor: move;
            border: 1px dashed #165dff;
          }
        }

        .modal {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, .6);
        }
      }

      .funBlock {
        position: absolute;
        right: 0;
        top: 0;
        padding: 10px 5px;
        width: 30px;
        font-size: 20px;
        text-align: center;
        background: white;
        border-radius: 5px;
        box-shadow: 0 0 5px #a9a9a9;
        z-index: 100;

        .icon {
          & + .icon {
            margin-top: 10px;
          }

          &:hover {
            color: #165dff;
            cursor: pointer;
          }
        }
      }
    }

    .btns {
      height: calc(~"100% - 10px");
      width: 300px;
      text-align: left;
      background: white;

      /deep/ .el-button {
        margin: 10px;
      }

      .funBtnItem {
        display: inline-block;
        padding: 20px 10px;
        width: calc(~"50% - 44px");
        text-align: center;
        cursor: pointer;

        .icon {
          font-size: 20px;
        }
      }
    }
  }

  .settingBlock {
    position: fixed;
    background: #fff;
    top: 50px;
    right: 10px;
    z-index: 100;

    .pseudoRow {
      position: absolute;
      content: "";
      left: -5px;
      border: 5px solid #fff;
      transform: rotateZ(45deg);
    }
  }
}
