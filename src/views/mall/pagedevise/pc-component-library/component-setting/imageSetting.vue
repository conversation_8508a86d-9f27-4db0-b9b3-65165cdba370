<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <div class="compSetting">
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">单图</p>
      <div slot="hint">提示：图片宽度自适应100%。</div>
      <div slot="mainContent">
        <el-form ref="form" label-width="90px" :model="formData">
          <el-form-item label="显示图片">
            <MaterialList :value="formData.imageUrl?[formData.imageUrl]:[]"
                          @sureSuccess="formData.imageUrl = $event?$event[0]:''"
                          @deleteMaterial="formData.imageUrl = ''"
                          type="image" shopId="-1"
                          :divStyle="'width:100%;margin-bottom:0px;height:90px;line-height: 90px;'"
                          :num=1></MaterialList>
          </el-form-item>
          <el-form-item label="显示高度">
            <el-input v-model="formData.height" size="mini" type="number" placeholder="高度">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="跳转链接">
          </el-form-item>
          <div style="padding: 0 0 5px 20px">
            <app-page-select :clientType="clientType" :isRefresh="true" :page="formData.pageUrl"
                             @change="formData.pageUrl=$event"></app-page-select>
          </div>
          <el-form-item label="页面下边距">
            <el-input v-model="formData.pageSpacing" size="mini" type="number"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
    </settingSlot>
    <!--    <p style="display:none">{{getData}}</p>-->
  </div>
</template>

<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';

import settingSlot from './settingSlot'
import MaterialList from '@/components/material/list.vue'
import AppPageSelect from '@/components/app-page-select/Index.vue'

export default {
  components: {settingSlot, MaterialList, AppPageSelect},
  data() {
    return {
      formDataCopy: {
        height: 300,
        imageUrl: '',
        pageSpacing: 0,
        pageUrl: ''
      },
      formData: {}
    };
  },
  props: {
    clientType: [String],
    showData: {
      type: Object,
      default: () => {
      }
    },
    config: {
      type: Object,
      default: () => {
      }
    }
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
    // that.updateData({
    //   componentsList: that.componentsList
    // })
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该组件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    }
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
    clientType() {
    }
  }
};
</script>
<style lang='less' scoped>
.compSetting {
}

</style>
