<!--
  - Copyright (C) 2018-2020
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <div>
    <settingSlot @confirm="confirm" @cancel="cancel" @reset="reset" :showReset="true">
      <p slot="dialogTitle">拼团</p>
      <div slot="hint">
      </div>
      <div slot="mainContent">
        <el-form ref="form" label-width="90px" :model="formData">
          <el-form-item label="标题图片">
            <MaterialList
              :value="formData.titleImage?[formData.titleImage]:[]"
              @sureSuccess="formData.titleImage = $event?$event[0]:''"
              @deleteMaterial="formData.titleImage = ''"
              type="image" shopId="-1" :num=1 :width="120" :height="48"></MaterialList>
          </el-form-item>
          <el-form-item label="标题描述">
            <el-input v-model="formData.subtitle" size="small" placeholder="副标题">
              <template slot="append">
                <el-color-picker size="mini" v-model="formData.subtitleColor"></el-color-picker>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="第一张图片" style="margin: 5px 0;">
            <MaterialList :value="formData.backgroundImg?[formData.backgroundImg]:[]"
                          @sureSuccess="formData.backgroundImg = $event?$event[0]:''"
                          @deleteMaterial="formData.backgroundImg = ''"
                          type="image" shopId="-1"
                          :divStyle="'width:100%;margin-bottom:0px;height:100px;line-height: 100px;'"
                          :num=1></MaterialList>
          </el-form-item>
          <el-form-item label="图片跳转" style="margin-top: -15px;">
          </el-form-item>
          <div style="padding: 0 0 5px 20px">
            <app-page-select :clientType="clientType" :page="formData.backgroundImgPageUrl"
                             @change="formData.backgroundImgPageUrl=$event"></app-page-select>
          </div>
          <el-form-item label="页面下边距">
            <el-input v-model="formData.pageSpacing" size="mini" type="number"
                      placeholder="页面下边距">
              <template slot="append">px</template>
            </el-input>
          </el-form-item>
          <el-form-item label="商品">
            <div>
              <el-button class="addBtn" type="primary" icon="el-icon-circle-plus" plain size="mini"
                         @click="showGoodsList">选择拼团商品
              </el-button>
            </div>
          </el-form-item>
          <div>
            <draggable v-model="formData.goodsList"
                       :options="{filter:'.notDraggable', preventOnFilter: false, animation:500}">
              <div v-for="(item,index) in formData.goodsList" :key="index" class="draggable-item">
                <el-row class="flex align-center">
                  <el-col :span="3" style="text-align: center;">
                    <div class="draggable-focus"><i class=" el-icon-d-caret"></i></div>
                    <div @click="delItem(index)" class="del-focus"><i class="delIcon el-icon-delete"></i></div>
                  </el-col>
                  <el-col :span="21">
                    <div class="notDraggable flex align-center" style="width: 100%">
                      <img :src="item.picUrl" style="width: 70px;height: 70px">
                      <el-input v-model="item.name" size="mini" placeholder="名称"
                                style="margin-left:10px;width:100%"></el-input>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </draggable>
          </div>
        </el-form>
      </div>
    </settingSlot>

    <el-dialog title="请选择商品" :visible.sync="dialogVisibleGoods" width="83%" top="20px" append-to-body>
      <avue-crud ref="crud"
                 v-model="form2"
                 :page.sync="page2"
                 :data="tableData2"
                 :table-loading="tableLoading2"
                 :option="tableOption2"
                 @on-load="getPage2"
                 @refresh-change="refreshChange2"
                 @sort-change="sortChange2"
                 @search-change="searchChange2"
                 @selection-change="selectionChange2">


        <template slot="picUrl" slot-scope="scope">
          <img style="height: 100px"
               :src="scope.row.picUrl">
        </template>
      </avue-crud>
      <span slot="footer" class="dialog-footer">
                  <el-button @click="dialogVisibleGoods = false">取 消</el-button>
                  <el-button type="primary" @click="subGoods">确 定</el-button>
                </span>
    </el-dialog>

  </div>
</template>

<script>

import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';
import draggable from "vuedraggable";

import settingSlot from './settingSlot'
import iconSelect from '../pages/page-components/iconSelect.vue'
import bgColorSelect from "../pages/page-components/bgColorSelect";
import MaterialList from '@/components/material/list.vue'
import AppPageSelect from '@/components/app-page-select/Index.vue'

import {getPage as getPage2, getObj, addObj, putObj, delObj} from '@/api/mall/grouponinfo'
import {tableOption2} from '@/const/crud/mall/grouponinfo'

export default {
  components: {draggable, settingSlot, iconSelect, bgColorSelect, MaterialList, AppPageSelect},
  data() {
    return {
      dialogVisibleGoods: false,
      shopList: [],
      selectionGoodsSpu: [],
      form2: {},
      tableData2: [],
      page2: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 20, // 每页显示多少条
        ascs: [],//升序字段
        descs: []//降序字段
      },
      paramsSearch2: {},
      tableLoading2: false,
      tableOption2: tableOption2,
      formDataCopy: {
        background: 'bg-white',
        backgroundImg: '',
        backgroundImgPageUrl: '',
        titleImage: '',
        title: '一起拼团',
        titleColor: '#333333',
        subtitle: '拼团声势大，天天享特价',
        subtitleColor: '#333333',
        titleIcon: 'cuIcon-message',
        pageSpacing: 0,
        goodsList: []
      },
      formData: {}
    };
  },
  props: {
    clientType: [String],
    showData: {
      type: Object,
      default: () => {
      }
    },
    config: {
      type: Object,
      default: () => {
      }
    },
    thememobile: {type: Object | Array},
  },
  computed: {
    ...mapState({
      componentsList: state => state.divpage.componentsList,
      clickComIndex: state => state.divpage.clickComIndex,
    })
  },
  mounted() {
    let that = this;
    if (that.IsEmptyObj(that.showData)) {
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
    } else {
      that.formData = that.showData
    }
    that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
  },
  methods: {
    ...mapMutations([
      'updateData'
    ]),
    addItem() {
      let that = this;
      that.pushItem()
    },
    pushItem() {
      let that = this;
      // if(that.formData.goodsList&&that.formData.goodsList.length >=10){
      //   that.$message.error("项目不能超过10条")
      //   return false;
      // }
      this.formData.goodsList.push({
        id: Math.random(),
        picUrls: [],
        name: '商品名',
        sellPoint: '',
        pointsGiveSwitch: '',
        freightTemplat: {},
        saleNum: 0,
        priceDown: '',
      })
    },
    // 删除项目
    delItem(index) {
      let that = this;
      if (that.formData.goodsList.length <= 1) {
        that.$message.error("请至少保留一条项目")
        return false;
      }
      that.$confirm('是否删除该项目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.formData.goodsList, index)
      }).catch(() => {
      })
    },
    // 删除按钮
    delBtn(index) {
      let that = this;
      that.$confirm('是否删除该组件?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$delete(that.componentsList[that.clickComIndex].data.itemArray, index)
        that.updateData({componentsList: that.componentsList});
      }).catch(() => {
      })
    },
    cancel() {
      this.$emit('cancel')
    },
    reset() {
      let that = this;
      that.formData = JSON.parse(JSON.stringify(that.formDataCopy))
      that.componentsList[that.clickComIndex]['data'] = this.formData;
    },
    confirm() {
      this.$emit('confirm', this.formData)
    },
    delete() {
      this.$emit('delete')
    },
    getPage2(page, params) {
      this.tableLoading2 = true
      getPage2(Object.assign({
        current: page.currentPage,
        size: page.pageSize,
        descs: this.page2.descs,
        ascs: this.page2.ascs,
      }, params, this.paramsSearch2)).then(response => {
        let tableData = response.data.data.records
        this.tableData2 = tableData
        this.page2.total = response.data.data.total
        this.page2.currentPage = page.currentPage
        this.page2.pageSize = page.pageSize
        this.tableLoading2 = false
        // 加载列表级联数据
        this.$nextTick(() => {
          this.$refs.crud.dicInit('cascader');
        })
      }).catch(() => {
        this.tableLoading2 = false
      })
    },
    searchChange2(params, done) {
      params = this.filterForm(params)
      this.paramsSearch2 = params
      this.page2.currentPage = 1
      this.getPage2(this.page2, params)
      done()
    },
    refreshChange2(val) {
      this.getPage2(val.page)
    },
    sortChange2(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : '';
      if (val.order == 'ascending') {
        this.page2.descs = []
        this.page2.ascs = prop
      } else if (val.order == 'descending') {
        this.page2.ascs = []
        this.page2.descs = prop
      } else {
        this.page2.ascs = []
        this.page2.descs = []
      }
      this.getPage(this.page)
    },
    selectionChange2(list) {
      this.selectionGoodsSpu = list
    },
    showGoodsList() {
      this.tableData2 = []
      this.dialogVisibleGoods = true
      this.getPage2(this.page2)
    },
    subGoods() {
      this.formData.goodsList = [...this.selectionGoodsSpu, ...this.formData.goodsList];
      let that = this;
      that.$set(that.componentsList[that.clickComIndex], 'data', this.formData)
      that.updateData({componentsList: that.componentsList});
      this.dialogVisibleGoods = false
    },
  },
  watch: {
    showData: {
      handler(newVal, oldVal) {
        this.formData = newVal ? newVal : this.formData;
      },
      deep: true
    },
  }
};
</script>
<style lang='less' scoped>
@import '../colorui/main.css';
@import '../colorui/icon.css';

.tm-select-bg {
  text-align: center;
  cursor: pointer;
  padding: 10px 0;
}

.tm-select-bg:hover {
  background: #efefef;
}

</style>
