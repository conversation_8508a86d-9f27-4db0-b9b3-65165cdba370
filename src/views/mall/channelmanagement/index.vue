<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
    <!--    <div class="execution">-->
    <!--        <basic-container>-->
    <!--            <avue-crud ref="crud"-->
    <!--                       :page="page"-->
    <!--                       :data="tableData"-->
    <!--                       :permission="permissionList"-->
    <!--                       :table-loading="tableLoading"-->
    <!--                       :option="tableOption"-->
    <!--                       v-model="form"-->
    <!--                       @on-load="getPage"-->
    <!--                       @refresh-change="refreshChange"-->
    <!--                       @row-update="handleUpdate"-->
    <!--                       @row-save="handleSave"-->
    <!--                       @row-del="handleDel"-->
    <!--                       @sort-change="sortChange"-->
    <!--                       @search-change="searchChange">-->
    <!--            </avue-crud>-->
    <!--        </basic-container>-->
    <!--    </div>-->
    <div class="execution">
        <BaseQueryInfo :data="queryInfo" @queryClick="queryClick"></BaseQueryInfo>
        <el-card class="box-card">
            <div slot="header" class="w100 df flr jc-fs alc">
                <el-button type="primary" icon="el-icon-plus" @click="clickCon('add')">新建</el-button>
            </div>
            <BaseTable :tableData="tableData" @tableClick="tableClick"></BaseTable>
        </el-card>
        <BaseFormBox :formConfig="formConfig" @formBack="formBack"></BaseFormBox>
        <el-dialog :title="tableConfig['title'][tableConfig['showStr']]"
                   :visible.sync="tableConfig['show']"
                   append-to-body
                   :width="tableConfig['width']"
                   @close="handleClose">
            <ServiceInfo v-if="tableConfig['showStr'] === 'serviceInfo'" :cid="serviceInfoId"></ServiceInfo>
            <ServiceDetails :cid="serviceInfoId" v-else-if="tableConfig['showStr'] === 'serviceDetails'"></ServiceDetails>
        </el-dialog>
    </div>
</template>

<!--新-->
<script>
import {getPage, getObj, addObj, putObj, delObj, generateWXCode, generateWXCode2} from '@/api/mall/channelmanagement'
import ServiceInfo from "@/views/mall/channelmanagement/ServiceInfo/ServiceInfo.vue";
import ServiceDetails from "@/views/mall/channelmanagement/ServiceDetails/ServiceDetails.vue";
import BaseQueryInfo from "@/components/BaseQueryInfo/BaseQueryInfo.vue";
import BaseTable from "@/components/BaseTable/BaseTable.vue";
import BaseFormBox from "@/components/BaseFormBox/BaseFormBox.vue";
import {deepCopy, isEmptyArr} from "@/util/otherUtils";

export default {
    name: 'channelmanagement',
    components: {
        ServiceInfo,
        ServiceDetails,
        BaseQueryInfo,
        BaseTable,
        BaseFormBox
    },
    data() {
        return {
            queryInfo: [
                {
                    label: '渠道名称',
                    value: '',
                    pla: "请输入内容",
                    type: "input"
                },
                {
                    label: '渠道来源',
                    value: '',
                    pla: "请选择",
                    props: {
                        label: "label",
                        value: "value"
                    },
                    options: [
                        {
                            label: "单体",
                            value: 1
                        },
                        {
                            label: "推广",
                            value: 2
                        }
                    ],
                    type: "select"
                },
                // {
                //     label: '创建时间',
                //     value: '',
                //     timeType: "daterange",
                //     valueFormat: "yyyy-MM-DD",
                //     type: "datePicker"
                // },
                {
                    label: '查询',
                    value: 'search',
                    btnType: "primary",
                    iconUrl: "el-icon-search",
                    type: "button"
                },
                {
                    label: '重置',
                    value: 'reset',
                    btnType: "default",
                    iconUrl: "el-icon-refresh",
                    type: "button"
                }
            ],
            tableData: {
                total: 10,
                page: 1,
                pageSize: 10,
                paginationVisible: true,
                data: [],
                columns: [
                    {
                        label: "序号",
                        slotName: "serialNum",
                        align: "center"
                    },
                    {
                        label: "渠道名称",
                        prop: "channelName",
                        align: "center"
                    },
                    {
                        label: "渠道来源",
                        prop: "channelCategory",
                        align: "center",
                        slotName: "customText",
                        textArr: ['', '单体', '推广']
                    },
                    {
                        label: "二维码",
                        prop: "qcCodeUrl",
                        slotName: "code2",
                        align: "center"
                    },
                    {
                        label: "服务量",
                        prop: "num",
                        align: "center"
                    },
                    {
                        label: "创建时间",
                        prop: "createdAt",
                        align: "center"
                    },
                    {
                        label: "操作",
                        slotName: "operation",
                        isDel: true,
                        align: "center",
                        btnList: [
                            {
                                text: '渠道信息',
                                type: "channelInfo",
                                color: 'rgba(0, 160, 233, 1)'
                            },
                            {
                                text: '渠道内容',
                                type: "serviceInfo",
                                color: 'rgba(0, 160, 233, 1)'
                            },
                            {
                                text: '渠道明细',
                                type: "serviceDetails",
                                color: 'rgba(0, 160, 233, 1)'
                            }
                        ]
                    }
                ]
            },
            formConfig: {
                title: {
                    add: "新增渠道",
                    edit: "编辑渠道"
                },
                type: "add",
                show: false,
                formList: [
                    {
                        label: "渠道名称",
                        prop: "channelName",
                        type: "input",
                        pla: "请输入渠道名称"
                    },
                    {
                        label: "渠道来源",
                        prop: "channelCategory",
                        pla: "请选择渠道来源",
                        type: "select",
                        options: [
                            {
                                label: "单体",
                                value: 1
                            },
                            {
                                label: "推广",
                                value: 2
                            }
                        ],
                        fieldNames: {
                            label: "label",
                            value: "value"
                        }
                    },
                    {
                        label: "渠道简介",
                        prop: "channelDescription",
                        type: "textarea",
                        pla: "请输入渠道简介",
                        allWidth: true
                    }
                ],
                formData: {
                    "id": "",
                    "channelName": "",
                    "channelDescription": "",
                    "channelCategory": 1,
                    "createdAt": "",
                    "updatedAt": "",
                    "qcCodeUrl": "",
                    "qcCodePath": "pages/goods/goods-detail/index"
                },
                formRules: {
                    channelName: [
                        {
                            required: true,
                            message: "请输入渠道名称",
                            trigger: "blur"
                        }
                    ],
                    channelCategory: [
                        {
                            required: true,
                            message: "请选择渠道来源",
                            trigger: "blur"
                        }
                    ]
                }
            },
            extraInfo: {
                formData: {
                    "id": "",
                    "channelName": "",
                    "channelDescription": "",
                    "channelCategory": 1,
                    "createdAt": "",
                    "updatedAt": "",
                    "qcCodeUrl": "",
                    "qcCodePath": "pages/goods/goods-detail/index"
                }
            },
            tableConfig: {
                show: false,
                showStr: "serviceInfo",
                width: "80%",
                title: {
                    serviceInfo: "渠道内容",
                    serviceDetails: "渠道明细"
                }
            },
            serviceInfoId: ""
        }
    },
    mounted() {
        this.getTableCon()
    },
    methods: {
        // 搜索点击
        async queryClick(info) {
            if(info.type === 'search') {
                await this.getTableCon()
            }
        },
        // 生成二维码
        async createQRCode(id) {
            const { data:res } = await generateWXCode2({
                "scene": id,
                "page": "extraJumpPages/ChannelRecommendation/ChannelRecommendation"
            })
            if(res.code === 0) {
                // this.formConfig.formData.qcCodeUrl = res.data
                await this.getTableCon()
            }
        },
        // 表单返回
        formBack(info) {
            if(info.type === 'close') {
                this.formConfig.show = false
            } else if(info.type === 'submit') {
                this.formConfig.formData = info.data
                this.addAndEditInfo()
            }
        },
        // 关闭弹框
        handleClose() {
            this.tableConfig.show = false
            this.tableConfig.showStr = ''
        },
        // 表格点击
        async tableClick(info) {
            this.tableConfig.showStr = info.typeStr
            if(info.typeStr === 'channelInfo') {
                this.formConfig.type = 'edit'
                this.formConfig.formData = info.data
                this.formConfig.show = true
            } else if(info.typeStr === 'serviceInfo' || info.typeStr === 'serviceDetails') {
                this.serviceInfoId = info.data.id
                this.tableConfig.show = true
            } else if(info.typeStr === 'del') {
                const { data:res } = await delObj(info.data.id)
                if(res.code === 0) {
                    await this.successCon(res.msg)
                }
            } else if(info.typeStr === 'qcCodeUrl') {
                await this.createQRCode(info.data.id)
            } else if(info.typeStr === 'page') {
                this.tableData.page = info.data.page
                this.tableData.pageSize = info.data.pageSize
                await this.getTableCon()
            }
        },

        // 点击事件
        clickCon(type) {
            if(type === 'add') {
                let extraInfoCopy = deepCopy(this.extraInfo)
                this.formConfig.type = 'add'
                this.formConfig.formData = extraInfoCopy.formData
                this.formConfig.show = true
            }
        },

        // 添加编辑渠道
        async addAndEditInfo() {
            let formConfigCopy = deepCopy(this.formConfig)
            let params = formConfigCopy.formData
            let res;
            if(formConfigCopy.type === 'add') {
                res = await addObj(params)
            } else if(formConfigCopy.type === 'edit') {
                res = await putObj(params)
            }
            if(res.data.code === 0) {
                await this.successCon(res.data.msg)
            }
        },

        // 请求成功后
        async successCon(message) {
            this.$message({
                message,
                type: 'success'
            });
            this.formConfig.show = false
            this.tableData.page = 1
            this.tableData.pageSize = 10
            await this.getTableCon()
        },

        // 获取表格列表
        async getTableCon() {
            let tableDataCopy = deepCopy(this.tableData)
            let queryInfoCopy = deepCopy(this.queryInfo)
            const { data:res } = await getPage({
                current: tableDataCopy.page,
                size: tableDataCopy.pageSize,
                channelName: queryInfoCopy[0]['value'],
                channelCategory: queryInfoCopy[1]['value']
                // startDate: isEmptyArr(queryInfoCopy[2]['value'])? queryInfoCopy[2]['value'][0] : "",
                // endDate: isEmptyArr(queryInfoCopy[2]['value'])?queryInfoCopy[2]['value'][1]:""
            })
            tableDataCopy['data'] = res['data']['records']
            tableDataCopy['total'] = res['data']['total']
            this.tableData = tableDataCopy
        }
    }
}
</script>

<!--旧-->
<!--<script>-->
<!--    import {getPage, getObj, addObj, putObj, delObj} from '@/api/mall/channelmanagement'-->
<!--    import {tableOption} from '@/const/crud/mall/channelmanagement'-->
<!--    import {mapGetters} from 'vuex'-->

<!--    export default {-->
<!--        name: 'channelmanagement',-->
<!--        data() {-->
<!--            return {-->
<!--                form: {},-->
<!--                tableData: [],-->
<!--                page: {-->
<!--                    total: 0, // 总页数-->
<!--                    currentPage: 1, // 当前页数-->
<!--                    pageSize: 20, // 每页显示多少条-->
<!--                    ascs: [],//升序字段-->
<!--                    descs: []//降序字段-->
<!--                },-->
<!--                paramsSearch: {},-->
<!--                tableLoading: false,-->
<!--                tableOption: tableOption-->
<!--            }-->
<!--        },-->
<!--        created() {-->
<!--        },-->
<!--        mounted: function () {-->
<!--        },-->
<!--        computed: {-->
<!--            ...mapGetters(['permissions']),-->
<!--            permissionList() {-->
<!--                return {-->
<!--                    addBtn: this.permissions['mall:channelmanagement:add'] ? true : false,-->
<!--                    delBtn: this.permissions['mall:channelmanagement:del'] ? true : false,-->
<!--                    editBtn: this.permissions['mall:channelmanagement:edit'] ? true : false,-->
<!--                    viewBtn: this.permissions['mall:channelmanagement:get'] ? true : false-->
<!--                };-->
<!--            }-->
<!--        },-->
<!--        methods: {-->
<!--            searchChange(params,done) {-->
<!--                params = this.filterForm(params)-->
<!--                this.paramsSearch = params-->
<!--                this.page.currentPage = 1-->
<!--                this.getPage(this.page, params)-->
<!--                done()-->
<!--            },-->
<!--            sortChange(val) {-->
<!--                let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''-->
<!--                if (val.order == 'ascending') {-->
<!--                    this.page.descs = []-->
<!--                    this.page.ascs = prop-->
<!--                } else if (val.order == 'descending') {-->
<!--                    this.page.ascs = []-->
<!--                    this.page.descs = prop-->
<!--                } else {-->
<!--                    this.page.ascs = []-->
<!--                    this.page.descs = []-->
<!--                }-->
<!--                this.getPage(this.page)-->
<!--            },-->
<!--            getPage(page, params) {-->
<!--                this.tableLoading = true-->
<!--                getPage(Object.assign({-->
<!--                    current: page.currentPage,-->
<!--                    size: page.pageSize,-->
<!--                    descs: this.page.descs,-->
<!--                    ascs: this.page.ascs,-->
<!--                }, params, this.paramsSearch)).then(response => {-->
<!--                    this.tableData = response.data.data.records-->
<!--                    this.page.total = response.data.data.total-->
<!--                    this.page.currentPage = page.currentPage-->
<!--                    this.page.pageSize = page.pageSize-->
<!--                    this.tableLoading = false-->
<!--                }).catch(() => {-->
<!--                    this.tableLoading = false-->
<!--                })-->
<!--            },-->
<!--            /**-->
<!--             * @title 数据删除-->
<!--             * @param row 为当前的数据-->
<!--             * @param index 为当前删除数据的行数-->
<!--             *-->
<!--             **/-->
<!--            handleDel: function (row, index) {-->
<!--                let _this = this-->
<!--                this.$confirm('是否确认删除此数据', '提示', {-->
<!--                    confirmButtonText: '确定',-->
<!--                    cancelButtonText: '取消',-->
<!--                    type: 'warning'-->
<!--                }).then(function () {-->
<!--                    return delObj(row.id)-->
<!--                }).then(data => {-->
<!--                    _this.$message({-->
<!--                        showClose: true,-->
<!--                        message: '删除成功',-->
<!--                        type: 'success'-->
<!--                    })-->
<!--                    this.getPage(this.page)-->
<!--                }).catch(function (err) {-->
<!--                })-->
<!--            },-->
<!--            /**-->
<!--             * @title 数据更新-->
<!--             * @param row 为当前的数据-->
<!--             * @param index 为当前更新数据的行数-->
<!--             * @param done 为表单关闭函数-->
<!--             *-->
<!--             **/-->
<!--            handleUpdate: function (row, index, done, loading) {-->
<!--                putObj(row).then(response => {-->
<!--                    this.$message({-->
<!--                        showClose: true,-->
<!--                        message: '修改成功',-->
<!--                        type: 'success'-->
<!--                    })-->
<!--                    done()-->
<!--                    this.getPage(this.page)-->
<!--                }).catch(() => {-->
<!--                    loading()-->
<!--                })-->
<!--            },-->
<!--            /**-->
<!--             * @title 数据添加-->
<!--             * @param row 为当前的数据-->
<!--             * @param done 为表单关闭函数-->
<!--             *-->
<!--             **/-->
<!--            handleSave: function (row, done, loading) {-->
<!--                addObj(row).then(response => {-->
<!--                    this.$message({-->
<!--                        showClose: true,-->
<!--                        message: '添加成功',-->
<!--                        type: 'success'-->
<!--                    })-->
<!--                    done()-->
<!--                    this.getPage(this.page)-->
<!--                }).catch(() => {-->
<!--                    loading()-->
<!--                })-->
<!--            },-->
<!--            /**-->
<!--             * 刷新回调-->
<!--             */-->
<!--            refreshChange(page) {-->
<!--                this.getPage(this.page)-->
<!--            }-->
<!--        }-->
<!--    }-->
<!--</script>-->

<style lang="scss" scoped>
.execution {
    padding: 20px;

    .box-card {
        margin-top: 20px;
    }
}
</style>
