<template>
    <div class="ServiceDetails">
<!--        <div class="sel-shop w100 df flr jc-fs alc">-->
<!--            <div class="shop-label dfc">-->
<!--                <p>店铺</p>-->
<!--            </div>-->
<!--            <el-select v-model="shopParams['shopId']" filterable clearable placeholder="请选择店铺" @change="shopChange">-->
<!--                <el-option-->
<!--                    v-for="(item,index) in shopArr"-->
<!--                    :key="index"-->
<!--                    :label="item.name"-->
<!--                    :value="item.id">-->
<!--                </el-option>-->
<!--            </el-select>-->
<!--        </div>-->
<!--        标签  总金额  支付金额  退款金额-->
        <el-descriptions class="statistic-info" :column="3" border>
            <el-descriptions-item v-for="(item,index) in statisticData" :key="index" :label="item['label']">{{ item['value'] }}</el-descriptions-item>
        </el-descriptions>

        <BaseTable :table-data="tableData" @tableClick="tableClick"></BaseTable>
    </div>
</template>

<script name="ServiceDetails">
import BaseTable from "@/components/BaseTable/BaseTable.vue";
import {getChannelOrder, getStoreList, serviceStatistics} from "@/api/mall/channelmanagement";
import {deepCopy} from "@/util/otherUtils";

export default {
    components: {BaseTable},
    props: {
        cid: {
            type: [String, Number],
            default: ''
        }
    },
    data() {
        return {
            shopArr: [],
            shopParams: {
                "shopId": "",
                "goodIds": []
            },
            params: {
                current: 1,
                size: 10
            },
            tableData: {
                total: 10,
                page: 1,
                pageSize: 10,
                paginationVisible: true,
                data: [],
                columns: [
                    {
                        label: "编号",
                        slotName: "serialNum",
                        align: "center"
                    },
                    {
                        label: "店铺名称",
                        prop: "shopName",
                        align: "center"
                    },
                    {
                        label: "商品主图",
                        prop: "picUrl",
                        align: "center",
                        slotName: "picShow2"
                    },
                    {
                        label: "价格",
                        prop: "salesPrice",
                        align: "center"
                    },
                    {
                        label: "数量",
                        prop: "quantity",
                        align: "center"
                    },
                    {
                        label: "运费金额（元）",
                        prop: "freightPrice",
                        align: "center"
                    },
                    {
                        label: "支付金额（元）",
                        prop: "paymentPrice",
                        align: "center"
                    },
                    {
                        label: "单价（元）",
                        prop: "salesPrice",
                        align: "center"
                    },
                    {
                        label: "状态",
                        prop: "status",
                        align: "center",
                        type: "tag",
                        textArr: ['正常', '退款中', '退货退款中', '完成退款', '完成退货退款'],
                        slotName: "customText"
                    },
                    {
                        label: "销售时间",
                        prop: "createTime",
                        align: "center"
                    },
                    {
                        label: "用户",
                        prop: "nickName",
                        align: "center"
                    }
                ]
            },
            statisticData: [
                {
                    label: "总金额（元）",
                    value: "",
                    type: "totalAmount"
                },
                {
                    label: "支付金额（元）",
                    value: "",
                    type: "paymentAmount"
                },
                {
                    label: "退款金额（元）",
                    value: "",
                    type: "refundAmount"
                }
            ]
        }
    },
    created() {
        this.getServiceStatistics()
    },
    mounted() {
        this.getShopList()
        this.getServiceInfo()
    },
    methods: {
        // 获取服务统计
        async getServiceStatistics() {
            const { data:res } = await serviceStatistics({ id: this.cid })
            let statisticDataCopy = deepCopy(this.statisticData)
            if(res.code === 0) {
                statisticDataCopy.map(item=>{
                    item['value'] = res.data[item.type] || 0
                })
            }
            this.statisticData = statisticDataCopy
        },

        // 店铺选择
        async shopChange() {
            await this.getServiceInfo()
        },
        // 获取店铺列表
        async getShopList() {
            const {data: res} = await getStoreList()
            if (res.code === 0) {
                this.shopArr = res.data
            }
        },
        // 获取服务明细数据
        async getServiceInfo() {
            let params = {
                current: this.tableData.page,
                size: this.tableData.pageSize,
                channelId: this.cid,
                shopId: this.shopParams['shopId']
            }
            const { data:res } = await getChannelOrder(params)
            if(res.code === 0) {
                this.tableData.data = res.data['records']
                this.tableData.total = res.data.total
            }
        },

        // 表格点击
        tableClick(info) {
            if(info.typeStr === 'page') {
                this.tableData.page = info.data.page
                this.tableData.pageSize = info.data.pageSize
                this.getServiceInfo()
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.statistic-info {
    width: 45%;
    margin-bottom: 10px;
}

.sel-shop {
    margin-bottom: 10px;

    .shop-label {
        margin-right: 10px;
    }
}
</style>
