<template>
    <div class="ServiceInfo">
        <BaseQueryInfo :data="queryInfo" @queryClick="queryClick"></BaseQueryInfo>
        <div class="service-tabs w100 df flr jc-fs alc">
            <template v-for="(item,index) in tabsData">
                <div class="service-box dfc" :key="index" @click="tabsClick(item)">
                    <p :class="tabsInd === item.value?'fc5':'fc2'">{{ item.label }}</p>
                </div>
            </template>
        </div>
        <BaseTable :table-data="tableData" @tableClick="tableClick"></BaseTable>
        <el-dialog
            width="60%"
            title="新增"
            :visible.sync="innerVisible"
            append-to-body>
            <div class="sel-shop w100 df flr jc-fs alc">
                <div class="shop-label dfc">
                    <p>店铺</p>
                </div>
                <el-select v-model="shopParams['shopId']" filterable placeholder="请选择店铺" @change="shopChange">
                    <el-option
                        v-for="(item,index) in shopArr"
                        :key="index"
                        :label="item.name"
                        :value="item.id">
                    </el-option>
                </el-select>
            </div>

            <BaseTable :table-data="goodsTableData" @tableClick="serviceTableClick" v-if="tabsInd !== 3"></BaseTable>
            <span slot="footer" class="dialog-footer">
                <el-button @click="innerVisible = false">取 消</el-button>
                <el-button type="primary" @click="shopConfirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script name="ServiceInfo">
import BaseQueryInfo from "@/components/BaseQueryInfo/BaseQueryInfo.vue";
import BaseTable from "@/components/BaseTable/BaseTable.vue";
import BaseFormBox from "@/components/BaseFormBox/BaseFormBox.vue";
import {
    channelGoods,
    channelGoodsDel,
    getProductList,
    getStoreList, qrcodeManagementDel,
    serviceInfoInquiry, soleGoodsSave, soleShopSave
} from "@/api/mall/channelmanagement";
import {deepCopy} from "@/util/otherUtils";

export default {
    props: {
        cid: {
            type: [String, Number],
            default: ''
        }
    },
    components: {
        BaseQueryInfo,
        BaseTable,
        BaseFormBox
    },
    data() {
        return {
            innerVisible: false,
            tabsInd: 1,
            tabsData: [
                {
                    label: "商品列表",
                    value: 1
                },
                {
                    label: "单商品",
                    value: 2
                },
                {
                    label: "单店铺",
                    value: 3
                }
            ],
            shopArr: [],
            shopParams: {
                "shopId": "",
                "goodIds": []
            },
            queryInfo: [
                {
                    label: '新建',
                    value: 'add',
                    btnType: "primary",
                    iconUrl: "el-icon-plus",
                    type: "button"
                }
            ],
            tableData: {
                total: 10,
                page: 1,
                pageSize: 10,
                paginationVisible: true,
                data: [],
                columns: [
                    {
                        label: "编号",
                        slotName: "serialNum",
                        align: "center"
                    },
                    {
                        label: "店铺名称",
                        prop: "shopName",
                        align: "center"
                    },
                    {
                        label: "商品主图",
                        prop: "picUrls",
                        align: "center",
                        slotName: "picShow2"
                    },
                    {
                        label: "商品名称",
                        prop: "goodsSpuName",
                        align: "center"
                    },
                    {
                        label: "价格",
                        prop: "priceDown",
                        subProp: "priceUp",
                        align: "center",
                        slotName: "customText2"
                    },
                    {
                        label: "编码",
                        prop: "spuCode",
                        align: "center"
                    },
                    {
                        label: "操作",
                        slotName: "operation",
                        isDel: true,
                        align: "center"
                    }
                ]
            },
            goodsParams: {
                current: 1,
                size: 10
            },

            goodsTableData: {
                total: 10,
                page: 1,
                pageSize: 10,
                paginationVisible: true,
                data: [],
                columns: [
                    {
                        label: "编号",
                        slotName: "selection",
                        align: "center"
                    },
                    {
                        label: "商品主图",
                        prop: "picUrls",
                        align: "center",
                        slotName: "picShow1"
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        align: "center"
                    },
                    {
                        label: "价格",
                        prop: "priceDown",
                        subProp: "priceUp",
                        align: "center",
                        slotName: "customText2"
                    },
                    {
                        label: "编码",
                        prop: "spuCode",
                        align: "center"
                    }
                ]
            },
        }
    },
    mounted() {
        this.getServiceInfo()
    },
    methods: {
        // 获取服务信息
        async getServiceInfo() {
            let params = {
                "current": this.tableData.page,
                "size": this.tableData.pageSize,
                type: this.tabsInd,
                channelId: this.cid
            }
            const {data: res} = await serviceInfoInquiry(params)
            if (res.code === 0) {
                this.tableData['data'] = res.data['records']
                this.tableData['total'] = res.data.total
            }
        },

        // 请求成功后
        async successCon(msg) {
            this.$message({
                msg,
                type: 'success'
            });
            this.innerVisible = false
            this.tableData.page = 1
            this.tableData.pageSize = 10
            await this.getServiceInfo()
        },

        // tabs点击
        async tabsClick(data) {
            if(data.value === 1) {
                this.tableData.columns = [
                    {
                        label: "店铺名称",
                        prop: "shopName",
                        align: "center"
                    },
                    {
                        label: "商品主图",
                        prop: "picUrls",
                        align: "center",
                        slotName: "picShow2"
                    },
                    {
                        label: "商品名称",
                        prop: "goodsSpuName",
                        align: "center"
                    },
                    {
                        label: "价格",
                        prop: "priceDown",
                        subProp: "priceUp",
                        align: "center",
                        slotName: "customText2"
                    },
                    {
                        label: "编码",
                        prop: "spuCode",
                        align: "center"
                    },
                    {
                        label: "创建时间",
                        prop: "createTime",
                        align: "center"
                    },
                    {
                        label: "操作",
                        slotName: "operation",
                        isDel: true,
                        align: "center"
                    }
                ]
            } else if(data.value === 2) {
                this.tableData.columns = [
                    {
                        label: "店铺名称",
                        prop: "shopName",
                        align: "center"
                    },
                    {
                        label: "商品主图",
                        prop: "picUrls",
                        align: "center",
                        slotName: "picShow2"
                    },
                    {
                        label: "商品名称",
                        prop: "goodsSpuName",
                        align: "center"
                    },
                    {
                        label: "价格",
                        prop: "priceDown",
                        subProp: "priceUp",
                        align: "center",
                        slotName: "customText2"
                    },
                    {
                        label: "二维码",
                        prop: "qrCodeUrl",
                        align: "center",
                        slotName: "picShow2"
                    },
                    {
                        label: "创建时间",
                        prop: "createTime",
                        align: "center"
                    },
                    {
                        label: "操作",
                        slotName: "operation",
                        isDel: true,
                        align: "center"
                    }
                ]
            } else if(data.value === 3) {
                this.tableData.columns = [
                    {
                        label: "店铺名称",
                        prop: "shopName",
                        align: "center"
                    },
                    {
                        label: "二维码",
                        prop: "qrCodeUrl",
                        align: "center",
                        slotName: "picShow2"
                    },
                    {
                        label: "创建时间",
                        prop: "createTime",
                        align: "center"
                    },
                    {
                        label: "操作",
                        slotName: "operation",
                        isDel: true,
                        align: "center"
                    }
                ]
            }
            this.tabsInd = data.value
            this.tableData.page = 1
            this.tableData.pageSize = 10
            await this.getServiceInfo()
        },

        // 内层弹框确定
        async shopConfirm() {
            let params = deepCopy(this.shopParams)
            let res
            params['channelId'] = this.cid
            if(this.tabsInd === 1) {
                res = await channelGoods(params)
            } else if(this.tabsInd === 2) {
                res = await soleGoodsSave(params)
            } else if(this.tabsInd === 3) {
                res = await soleShopSave(params)
            }
            if (res.data.code === 0) {
                await this.successCon(res.data.msg)
            }
        },

        // 内层表格点击
        serviceTableClick(info) {
            if (info.typeStr === 'selectionChange') {
                let arr = []
                info.data.map(item => {
                    arr.push(item.id)
                })
                this.shopParams.goodIds = arr
            } else if(info.typeStr === 'page') {
                this.goodsTableData.page = info.data.page
                this.goodsTableData.pageSize = info.data.pageSize
                this.getProductList()
            }
        },

        // 商品列表
        async getProductList() {
            let params = deepCopy(this.goodsParams)
            params['shopId'] = this.shopParams['shopId']
            const {data: res} = await getProductList(params)
            if (res.code === 0) {
                this.goodsTableData.data = res.data['records']
                this.goodsTableData.total = res.data['total']
            }
        },
        // 店铺选择
        async shopChange() {
            await this.getProductList()
        },

        // 获取店铺列表
        async getShopList() {
            const {data: res} = await getStoreList()
            if (res.code === 0) {
                this.shopArr = res.data
            }
        },

        // 表格点击
        async tableClick(info) {
            if (info.typeStr === 'del') {
                let res
                if(this.tabsInd === 1) {
                    res = await channelGoodsDel({id: info.data.id})
                } else if(this.tabsInd > 1) {
                    res = await qrcodeManagementDel({id: info.data.id})
                }
                if (res && res.data && res.data.code === 0){
                    await this.successCon(res.data.msg)
                }
            } else if(info.typeStr === 'page') {
                this.tableData.page = info.data.page
                this.tableData.pageSize = info.data.pageSize
                await this.getServiceInfo()
            }
        },

        // 搜索点击
        queryClick(data) {
            if (data.type === 'add') {
                this.shopParams = {
                    "shopId": "",
                    "goodIds": []
                }
                this.goodsTableData.data = []
                this.innerVisible = true
                this.getShopList()
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.ServiceInfo {
    .service-tabs {
        .service-box {
            padding: 10px;
            cursor: pointer;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
    }
}


.sel-shop {
    margin-bottom: 10px;

    .shop-label {
        margin-right: 10px;
    }
}
</style>
