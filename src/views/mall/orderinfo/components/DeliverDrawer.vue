<template>
    <el-drawer
        :visible="visible"
        title="配送员信息"
        size="50%"
        :close-on-press-escape="false"
        :wrapperClosable="false"
        @close="handleCancel"
    >
        <div>
            <div class="couriers-container">
                <couriers ref="couriers" isSingle/>
            </div>
            <div class="utils">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm"
                    >确认</el-button
                >
            </div>
        </div>
    </el-drawer>
</template>

<script>
import couriers from "@/views/mall/couriers/index.vue";
export default {
    name: "DeliverDrawer",
    components: {
        couriers,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        handleCancel() {
            this.$emit("update:visible", false);
            this.$emit("cancel");
        },
        handleConfirm () {
            const ids = this.$refs.couriers.selectionIds;
            if (ids.length === 0) {
                this.$message.warning("请选择要添加的配送员");
                return;
            }
            const currentCourier = this.$refs.couriers.tableData.find(item => ids[0] == item.id);
            console.log(currentCourier, "currentCourier");
            if(currentCourier.status == 0){
                this.$message.warning("配送员状态为关闭，请选择其他配送员！");
                return
            }

            this.$emit("confirm", currentCourier);
        },
    },
    watch: {
        visible: {
            handler (newVal) {
                if (newVal) {
                    console.log('okokok');
                    this.$refs.couriers.clearSelection();
                }
            },
        },
    },
};
</script>

<style lang="scss" scoped>
.utils {
    padding: 20px;
    text-align: right;
}
</style>
