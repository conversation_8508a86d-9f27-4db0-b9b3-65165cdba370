<template>
  <div class="execution">
    <basic-container>
      <avue-crud ref="crud" :page="page" :data="tableData" :permission="permissionList" :table-loading="tableLoading"
        :option="tableOption" v-model="form" @on-load="getPage" @refresh-change="refreshChange"
        @row-update="handleUpdate" @row-save="handleSave" @row-del="handleDel" @sort-change="sortChange"
        @search-change="searchChange" @selection-change="selectionChange">
        <template slot="menu">
          <div style="float: left;margin-right: 10px;">
            <el-date-picker v-model="date" type="datetimerange" :picker-options="pickerOptions"
              @change="dateChange(date)" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始时间"
              end-placeholder="结束时间" align="left" size="small">
            </el-date-picker>
          </div>
        </template>
        <template slot="searchMenu">
          <el-button type="primary" icon="el-icon-plus" size="small" @click.stop="drawerAdd=true">新增</el-button>
          <el-button type="success" @click="batchVerify('3')" v-if="permissions['mall:videopopular:release']" size="small" icon="el-icon-top"
            >发布视频
          </el-button>
        </template>
        <template slot="menu" slot-scope="scope">
          <el-button type="text" size="small" icon="el-icon-edit" @click.stop="handleEdit(scope.row,scope.index)">编辑
          </el-button>
          <el-button v-show="scope.row.videoStatus == 1 || scope.row == 4" type="text"
            v-if="permissions['mall:videopopular:release']" size="small" plain @click="updateVideoStatus(scope.row,2)"
            icon="el-icon-top">发布视频</el-button>
          <el-button v-show="scope.row.videoStatus == 2" type="text" v-if="permissions['mall:videopopular:examine']"
            size="small" plain icon="el-icon-finished" @click="showShen(scope.row,3)">审核</el-button>
          <el-button v-show="scope.row.videoStatus == 3" type="text" v-if="permissions['mall:videopopular:cancel']"
            size="small" plain icon="el-icon-bottom" @click="updateVideoStatus(scope.row,1)">取消发布</el-button>
        </template>
      </avue-crud>
    </basic-container>

    <el-drawer :title="formAdd.id?'修改科普视频':'新增科普视频'" :visible.sync="drawerAdd" :direction="'rtl'" size="88%"
      :destroy-on-close="true" @close="closeAdd">
      <div style="padding: 0px 50px 0;">
        <div style="width: 900px;">
          <el-tabs v-model="activeName">
            <el-tab-pane label="基本信息" name="1">
              <avue-form ref="formRef" :option="formOption" v-model="formAdd">
                <template #hostGraphUrl="{ disabled, size }">
                  <div>
                    <MaterialList :value="formAdd.hostGraphUrl" v-model="formAdd.hostGraphUrl" type="image" :num=1
                      shopId="-1" @sureSuccess="imgSave"></MaterialList>
                    <P>建议尺寸：600*430</P>
                  </div>
                </template>
                <template #videoUrl="{ disabled, size }">
                  <div>
                    <MaterialList :value="formAdd.videoUrl" v-model="formAdd.videoUrl" type="video" :num=1 shopId="-1">
                    </MaterialList>
                    <P>支持mp4格式视频，大小不能超过50Mb</P>
                  </div>
                </template>
              </avue-form>
            </el-tab-pane>
            <el-tab-pane label="视频简介" name="2">
              <avue-form ref="formJjRef" :option="formJjOption" v-model="formAdd">
                <template #brief="{ disabled, size }">
                  <div>
                    <BaseEditor v-model="formAdd.brief" />
                  </div>
                </template>
              </avue-form>
            </el-tab-pane>
          </el-tabs>
          <div style="text-align: center;">
            <el-button type="primary" v-if="activeName=='1'" icon="el-icon-right"
              @click="activeName='2'">下一步</el-button>
            <el-button type="primary" v-if="activeName=='2'&&!formAdd.id" icon="el-icon-circle-check"
              @click="handleSave">保存</el-button>
            <el-button type="primary" v-if="activeName=='2'&&formAdd.id" icon="el-icon-circle-check"
              @click="handleUpdate">修改</el-button>
            <el-button @click="drawerAdd=false" icon="el-icon-circle-close">取消</el-button>
          </div>
        </div>
      </div>
    </el-drawer>

    <el-dialog title="科普视频审核" :visible.sync="dialogFormVisible" :destroy-on-close="false" @close="closeShen">
      <avue-form :option="formOptionSh" v-model="formAdd">
        <template #hostGraphUrl="{ disabled, size }">
          <div>
            <MaterialList :value="formAdd.hostGraphUrl" v-model="formAdd.hostGraphUrl" :disabled="true" type="image"
              :num=1 shopId="-1" @sureSuccess="imgSave"></MaterialList>
          </div>
        </template>
        <template #videoUrl="{ disabled, size }">
          <div>
            <MaterialList :value="formAdd.videoUrl" v-model="formAdd.videoUrl" type="video" :disabled="true" :num=1
              shopId="-1">
            </MaterialList>
          </div>
        </template>
        <template #brief="{ disabled, size }">
          <div>
            <el-button type="primary" @click="dialogSpjjVisible=true">预览</el-button>
          </div>
        </template>
      </avue-form>
      <div style="border-top: 1px solid #E4E7ED;">
        <avue-form ref="formRefSh" :option="formOptionJg" v-model="formJg">
          <template #reject="{ disabled, size }">
            <div v-if="formJg.videoStatus=='4'">
              <el-input v-model="formJg.reject" type="textarea" placeholder="请输入驳回原因"></el-input>
            </div>
          </template>
        </avue-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="shenhe">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="视频简介预览" :visible.sync="dialogSpjjVisible" :destroy-on-close="false" @close="closeShen">
      <div>
        <div v-html="formAdd.brief"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogSpjjVisible = false">关闭</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import {
    getPage,
    getObj,
    addObj,
    putObj,
    delObj,
    changeObj,
    putObjVerify
  } from '@/api/mall/videopopular'
  import {
    tableOption
  } from '@/const/crud/mall/videopopular'
  import {
    mapGetters
  } from 'vuex'
  import MaterialList from '@/components/material/list.vue'
  import BaseEditor from '@/components/editor/BaseEditor.vue'
  export default {
    name: 'videopopular',
    components: {
      MaterialList,
      BaseEditor
    },
    data() {
      return {
        dialogSpjjVisible: false,
        form: {},
        tableData: [],
        verifyStatus: '1',
        date: ['', ''],
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 20, // 每页显示多少条
          ascs: [], //升序字段
          descs: [] //降序字段
        },
        formJg: {},
        formAdd: {
          hostGraphUrl: [],
          videoUrl: []
        },
        paramsSearch: {},
        tableLoading: false,
        tableOption: tableOption,
        drawerAdd: false,
        activeName: '1',
        pickerOptions: {},
        formJjOption: {
          emptyBtn: false,
          submitBtn: false,
          span: 24,
          column: [{
            label: '视频简介',
            prop: 'brief'
          }]
        },
        dialogFormVisible: false,
        formOption: {
          emptyBtn: false,
          submitBtn: false,
          span: 24,
          column: [{
              label: '视频标题',
              prop: 'title',
              rules: [{
                required: true,
                message: '请输入视频标题',
                trigger: 'blur'
              }]
            },
            {
              label: '创建人',
              prop: 'userName'
            },
            {
              label: '视频主图',
              prop: 'hostGraphUrl',
              rules: [{
                required: true,
                message: '请上传视频主图',
                trigger: 'change'
              }]
            },
            {
              label: '视频',
              prop: 'videoUrl',
              rules: [{
                required: true,
                message: '请上传视频',
                trigger: 'change'
              }]
            },
            {
              label: '视频分类',
              prop: 'vtId',
              type: 'select',
              props: {
                label: 'vtName',
                value: 'id'
              },
              dicUrl: '/mall/videocategory/list',
              rules: [{
                required: true,
                message: '请选择视频分类',
                trigger: 'change'
              }]
            },
            {
              label: '序号',
              prop: 'sort',
              type: 'number',
              rules: [{
                required: true,
                message: '请输入序号',
                trigger: 'blur'
              }]
            },
            {
              label: '视频点赞',
              prop: 'likesNum',
              type: 'number'
            },
            {
              label: '视频播放量',
              prop: 'playNum',
              type: 'number'
            },
            {
              label: '视频评论',
              prop: 'commentStatus',
              type: 'radio',
              dicData: [{
                  label: '开启',
                  value: '0'
                },
                {
                  label: '关闭',
                  value: '1'
                }
              ],
              rules: [{
                required: true,
                message: '请选择视频评论是否开启',
                trigger: 'blur'
              }]
            }
          ]
        },
        formOptionJg: {
          emptyBtn: false,
          submitBtn: false,
          span: 24,
          column: [{
              label: '审核结果',
              prop: 'videoStatus',
              type: 'radio',
              dicData: [{
                  label: '审核通过',
                  value: 3
                },
                {
                  label: '审核驳回',
                  value: 4
                }
              ],
              rules: [{
                required: true,
                message: '请选择审核结果',
                trigger: 'blur'
              }],
              change: (obj) => {
                if (obj.value == 4) {
                  this.formOptionJg.column[1].display = true
                } else {
                  this.formOptionJg.column[1].display = false
                }
              }
            },
            {
              label: '驳回原因',
              prop: 'reject',
              display: false
            }
          ]
        },
        formOptionSh: {
          emptyBtn: false,
          submitBtn: false,
          span: 24,
          column: [{
              label: '视频标题',
              prop: 'title',
              disabled: true,
              rules: [{
                required: true,
                message: '请输入视频标题',
                trigger: 'blur'
              }]
            },
            {
              label: '视频主图',
              prop: 'hostGraphUrl',
              disabled: true,
              rules: [{
                required: true,
                message: '请上传视频主图',
                trigger: 'change'
              }]
            },
            {
              label: '视频',
              prop: 'videoUrl',
              disabled: true,
              rules: [{
                required: true,
                message: '请上传视频',
                trigger: 'change'
              }]
            },
            {
              label: '视频分类',
              prop: 'vtId',
              type: 'select',
              disabled: true,
              props: {
                label: 'vtName',
                value: 'id'
              },
              dicUrl: '/mall/videocategory/list',
              rules: [{
                required: true,
                message: '请选择视频分类',
                trigger: 'change'
              }]
            },
            {
              label: '序号',
              prop: 'sort',
              disabled: true,
              type: 'number',
              rules: [{
                required: true,
                message: '请输入序号',
                trigger: 'blur'
              }]
            },
            {
              label: '视频点赞',
              prop: 'likesNum',
              disabled: true,
              type: 'number'
            },
            {
              label: '视频播放量',
              prop: 'playNum',
              disabled: true,
              type: 'number'
            },
            {
              label: '视频评论',
              prop: 'commentStatus',
              disabled: true,
              type: 'radio',
              dicData: [{
                  label: '开启',
                  value: '0'
                },
                {
                  label: '关闭',
                  value: '1'
                }
              ],
              rules: [{
                required: true,
                message: '请选择视频评论是否开启',
                trigger: 'blur'
              }]
            },
            {
              label: '简介',
              prop: 'brief',
              disabled: true,
              type: 'input'
            },
          ]
        },
        selectionData: ''

      }
    },
    created() {},
    mounted: function() {},
    computed: {
      ...mapGetters(['permissions']),
      permissionList() {
        return {
          addBtn: this.permissions['mall:videopopular:add'] ? true : false,
          delBtn: this.permissions['mall:videopopular:del'] ? true : false,
          editBtn: this.permissions['mall:videopopular:edit'] ? true : false,
          viewBtn: this.permissions['mall:videopopular:get'] ? true : false
        };
      }
    },
    methods: {
      selectionChange(list) {
        this.selectionData = list
      },
      batchVerify(verifyStatus) {
        if (this.selectionData.length <= 0) {
          this.$message.error('请选择视频')
          return
        }
        var _this=this;
        this.$confirm('是否确认发布选中的视频', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          let selection=[]
          _this.selectionData.forEach(item => {
            selection.push({
              id:item.id,
              videoStatus:verifyStatus
            })
          })
          putObjVerify(selection).then(data => {
            _this.getPage(_this.page)
          })
        })
      },
      handleEdit(row, index) {
        // this.$refs.crud.rowEdit(row, index);
        this.formAdd = row;
        // obj.hostGraphUrl = this.formAdd.hostGraphUrl[0];
        // obj.videoUrl = this.formAdd.videoUrl[0];
        this.formAdd.videoUrl = [row.videoUrl]
        this.formAdd.hostGraphUrl = [row.hostGraphUrl]
        this.drawerAdd = true;
      },
      searchChange(params, done) {
        params = this.filterForm(params)
        if (params.releaseTime && params.releaseTime.length == 2) {
          params.startTime = params.releaseTime[0];
          params.endTime = params.releaseTime[1]
          delete params.releaseTime
        }
        this.paramsSearch = params
        this.page.currentPage = 1
        this.getPage(this.page, params)
        done()
      },
      sortChange(val) {
        let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : ''
        if (val.order == 'ascending') {
          this.page.descs = []
          this.page.ascs = prop
        } else if (val.order == 'descending') {
          this.page.ascs = []
          this.page.descs = prop
        } else {
          this.page.ascs = []
          this.page.descs = []
        }
        this.getPage(this.page)
      },
      // 图片赋值
      imgSave(urls) {
        console.log(urls)
      },
      getPage(page, params) {
        this.tableLoading = true
        getPage(Object.assign({
          current: page.currentPage,
          size: page.pageSize,
          descs: this.page.descs,
          ascs: this.page.ascs,
        }, params, this.paramsSearch)).then(response => {
          this.tableData = response.data.data.records
          this.page.total = response.data.data.total
          this.page.currentPage = page.currentPage
          this.page.pageSize = page.pageSize
          this.tableLoading = false
        }).catch(() => {
          this.tableLoading = false
        })
      },
      /**
       * @title 数据删除
       * @param row 为当前的数据
       * @param index 为当前删除数据的行数
       *
       **/
      handleDel: function(row, index) {
        let _this = this
        this.$confirm('是否确认删除此数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(function() {
          return delObj(row.id)
        }).then(data => {
          _this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success'
          })
          this.getPage(this.page)
        }).catch(function(err) {})
      },
      /**
       * 视频状态
       */
      updateVideoStatus: function(row, status) {
        // 传参 ：id，videoStatus-视频状态：1：未发布 2：待审核 3：已发布 4：审核驳回，reject-驳回原因
        var obj = {
          id: row.id,
          videoStatus: status
        };
        var msg = ['', '取消发布', '提交发布', '发布', '驳回']
        changeObj(obj).then(response => {
          this.$message({
            showClose: true,
            message: msg[status] + '成功',
            type: 'success'
          })
          this.getPage(this.page)
        }).catch(() => {
          // loading()
        })
      },
      /**
       * @title 数据更新
       * @param row 为当前的数据
       * @param index 为当前更新数据的行数
       * @param done 为表单关闭函数
       *
       **/
      handleUpdate: function(row, index, done, loading) {
        var obj = this.formAdd;
        delete obj.$commentStatus;
        delete obj.$vtId;
        obj.hostGraphUrl = this.formAdd.hostGraphUrl[0];
        obj.videoUrl = this.formAdd.videoUrl[0];
        putObj(obj).then(response => {
          this.$message({
            showClose: true,
            message: '修改成功',
            type: 'success'
          })
          this.drawerAdd = false;
          this.formAdd = {
            hostGraphUrl: [],
            videoUrl: []
          };
          this.activeName = '1';
          // done()
          this.getPage(this.page)
        }).catch(() => {
          // loading()
        })
      },
      /**
       * @title 数据添加
       * @param row 为当前的数据
       * @param done 为表单关闭函数
       *
       **/
      handleSave: function() {
        var obj = this.formAdd;
        delete obj.$commentStatus;
        delete obj.$vtId;
        obj.hostGraphUrl = this.formAdd.hostGraphUrl[0];
        obj.videoUrl = this.formAdd.videoUrl[0];
        addObj(obj).then(response => {
          this.$message({
            showClose: true,
            message: '添加成功',
            type: 'success'
          })
          this.drawerAdd = false;
          this.formAdd = {
            hostGraphUrl: [],
            videoUrl: []
          };
          this.activeName = '1';
          this.getPage(this.page)
        }).catch(() => {
          // loading()
        })
      },
      /**
       * 刷新回调
       */
      refreshChange(page) {
        this.getPage(this.page)
      },
      /**
       * 关闭新增/编辑页面
       */
      closeAdd() {
        this.formAdd = {
          hostGraphUrl: [],
          videoUrl: []
        };
        this.activeName = '1';
      },
      // 显示审核的弹窗
      showShen(row) {
        this.formAdd = row;
        this.formAdd.videoUrl = [row.videoUrl]
        this.formAdd.hostGraphUrl = [row.hostGraphUrl]
        this.formJg = {
          id: row.id
        }
        this.dialogFormVisible = true;
      },
      // 审核保存
      shenhe() {
        this.$refs.formRefSh.validate((valid) => {
          if (valid) {
            var msg = ['', '取消发布', '提交发布', '发布', '驳回']
            delete this.formJg.$videoStatus
            changeObj(this.formJg).then(response => {
              this.$message({
                showClose: true,
                message: msg[this.formJg.videoStatus] + '成功',
                type: 'success'
              })
              this.dialogFormVisible = false;
              this.getPage(this.page)
            }).catch(() => {
              // loading()
            })
          }
        })
      },
      // 关闭审核弹窗
      closeShen() {
        this.formJg = {};
        this.formAdd = {
          hostGraphUrl: [],
          videoUrl: []
        };
        this.activeName = '1';
      }
    }
  }
</script>

<style lang="scss" scoped></style>
