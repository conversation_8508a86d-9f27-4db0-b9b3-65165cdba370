<template>
  <div>
    <el-button
      v-if="id"
      class="cursor-pointer"
      size="small"
      type="text"
      @click="showDetail"
    >{{ name || detail.name || id }}
    </el-button>
    <el-dialog
      title="门店详情"
      :visible.sync="dialogVisibleDetail"
      :append-to-body="true"
      width="66%"
    >
      <div style="position: relative" v-if="dialogVisibleDetail">
        <avue-form ref="formRef" :option="tableOption" v-model="detail">
          <template slot="imgUrl">
            <div>
              <el-image :src="detail.imgUrl" style="width: 100px; height: 100px" />
            </div>
          </template>
        </avue-form>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getObj } from "@/api/mall/shopstore";
import { tableOptionDetail } from "@/const/crud/mall/shopstore";
export default {
  props: {
    id: {
      type: String,
      default: ""
    },
    name: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tableOption: tableOptionDetail,
      dialogVisibleDetail: false,
      detail: {}
    };
  },
  watch: {
    id: {
      handler() {
        this.getDetail();
      },
      immediate: true
    }
  },
  methods: {
    getDetail() {
      if (this.id) {
        getObj(this.id).then(res => {
          this.detail = res.data.data ? res.data.data : {};
        });
      }
    },
    showDetail() {
      if (this.detail.id) {
        this.dialogVisibleDetail = true;
        return;
      }
      if (!this.id) {
        this.$message({
          type: 'warning',
          message: '没有查询到数据!'
        });
        return;
      }
      getObj(this.id).then(res => {
        this.detail = res.data.data ? res.data.data : {};
        if (!this.detail.id) {
          this.$message({
            type: 'warning',
            message: '没有查询到数据!'
          });
        } else {
          this.dialogVisibleDetail = true;
        }
      });
    }
  }
};
</script>
