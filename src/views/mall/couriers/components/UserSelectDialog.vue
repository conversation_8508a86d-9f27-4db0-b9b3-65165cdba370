<template>
    <el-dialog
        title="选择用户"
        :visible="visible"
        width="60%"
        @close="handleClose"
        append-to-body
    >
        <div class="search-container">
            <el-input
                v-model="queryParams.keyword"
                placeholder="请输入用户姓名或手机号码"
                style="width: 300px"
                clearable
                @keyup.enter.native="handleSearch"
            >
                <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
            </el-input>
        </div>
        <el-table
            :data="tableData"
            v-loading="loading"
            @row-click="handleRowClick"
            highlight-current-row
        >
            <el-table-column
                type="radio"
                width="55"
            ></el-table-column>
            <el-table-column
                prop="id"
                label="用户ID"
                width="180"
            ></el-table-column>
            <el-table-column
                prop="nickName"
                label="用户姓名"
                width="180"
            ></el-table-column>
            <el-table-column
                prop="phone"
                label="手机号码"
            ></el-table-column>
            <el-table-column
                prop="province"
                label="省份"
            ></el-table-column>
        </el-table>
        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryParams.current"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="queryParams.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
            >
            </el-pagination>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { getPage } from '@/api/mall/userinfo'
export default {
    name: 'UserSelectDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: false,
            selectedRow: null,
            tableData: [],
            total: 0,
            queryParams: {
                current: 1,
                size: 10,
                keyword: ''
            }
        }
    },
    methods: {
        getUserList() {
            this.loading = true
            getPage(this.queryParams).then(res => {
                this.tableData = res.data.data.records
                this.total = res.data.data.total
                this.loading = false
            })
        },
        handleSizeChange(val) {
            this.queryParams.size = val
            this.getUserList()
        },
        handleCurrentChange(val) {
            this.queryParams.current = val
            this.getUserList()
        },
        handleClose() {
            this.selectedRow = null
            this.$emit('update', false)
        },
        handleRowClick(row) {
            this.selectedRow = row
        },
        handleConfirm() {
            if (this.selectedRow) {
                this.$emit('select', this.selectedRow)
                this.handleClose()
            } else {
                this.$message.warning('请选择一个用户')
            }
        },
        handleSearch() {
            this.queryParams.current = 1
            this.getUserList()
        }
    },
    created() {
        this.getUserList()
    },
    watch: {
        visible: {
            handler(newVal) {
                if (newVal) {
                    this.getUserList()
                }
            }
        }
    }
}
</script>

<style lang="less" scoped>
.search-container {
    margin-bottom: 15px;
}
.pagination-container {
    padding: 15px 0;
    text-align: right;
}
/deep/ .el-table__row {
    cursor: pointer;
}
</style>
