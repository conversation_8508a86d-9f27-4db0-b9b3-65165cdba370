<template>
    <el-dialog
        :title="dialogType === 'add' ? '添加配送员' : '编辑配送员'"
        :visible.sync="visible"
        width="50%"
        @close="handleClose"
        append-to-body
    >
        <el-form
            :model="form"
            :rules="rules"
            ref="form"
            label-width="120px"
        >
            <el-form-item label="配送员姓名" prop="couriersName">
                <el-input v-model="form.couriersName"></el-input>
            </el-form-item>
            <el-form-item label="配送员电话" prop="couriersPhone">
                <el-input v-model="form.couriersPhone"></el-input>
            </el-form-item>
            <el-form-item label="用户ID" prop="uid">
                <el-input v-model="form.uid"></el-input>
                <el-button type="text" @click="handleSearchUser">查找</el-button>
            </el-form-item>
            <!-- <el-form-item label="序号" prop="number">
                <el-input type="number" v-model="form.number"></el-input>
                <span class="form-tip">用户排序，越大越靠前</span>
            </el-form-item> -->
            <el-form-item label="状态" prop="status">
                <el-switch
                    v-model="form.status"
                    active-value="1"
                    inactive-value="0"
                ></el-switch>
                <span class="form-tip">{{ form.status === '1' ? '已开启' : '已关闭' }}</span>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
        </div>
        <user-select-dialog
            :visible="userSelectVisible"
            @update="userSelectVisible = $event"
            @select="handleUserSelect"
        />
    </el-dialog>
</template>

<script>
import UserSelectDialog from './UserSelectDialog.vue'

export default {
    name: 'CouriersDialog',
    components: {
        UserSelectDialog
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        dialogType: {
            type: String,
            default: 'add'
        },
        editData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            form: {
                couriersName: '',
                couriersPhone: '',
                uid: '',
                status: '1'
            },
            rules: {
                couriersName: [
                    { required: true, message: '请输入配送员姓名', trigger: 'blur' }
                ],
                couriersPhone: [
                    { required: true, message: '请输入配送员电话', trigger: 'blur' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
                ],
                uid: [
                    { required: true, message: '请输入用户ID', trigger: 'change' }
                ]
            },
            loading: false,
            userSelectVisible: false
        }
    },
    watch: {
        visible(val) {
            // if (val && this.dialogType === 'edit') {
                this.form = { ...this.editData }
            // }
        }
    },
    methods: {
        handleClose() {
            this.$refs.form.resetFields()
            this.$emit('update:visible', false)
            this.loading = false
        },
        handleSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    this.loading = true
                    this.$emit('submit', {
                        type: this.dialogType,
                        data: this.form
                    })
                }
            })
        },
        handleSearchUser() {
            this.userSelectVisible = true
        },
        handleUserSelect(user) {
            // this.form.uid = user.id
            this.$set(this.form,'uid',user.id)

            // this.form.couriersName = user.nickName
            // this.form.couriersPhone = user.phone
        }
    }
}
</script>

<style lang="less" scoped>
.form-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
}
</style>
