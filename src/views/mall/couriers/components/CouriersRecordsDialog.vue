<template>
    <el-dialog
        title="配送记录"
        :visible.sync="visible"
        width="90%"
        :before-close="handleClose"
        append-to-body
        class="couriers-records-dialog"
    >
        <div class="filter-container">
            <div class="left">
                <!-- <span>配送类型：</span>
                <el-select
                    v-model="filterForm.deliveryType"
                    placeholder="配送类型"
                    size="small"
                    @change="handleSearch"
                >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="系统配送" value="1"></el-option>
                    <el-option label="码科配送" value="2"></el-option>
                </el-select> -->
                <span>配送状态：</span>
                <el-select
                    v-model="filterForm.status"
                    placeholder="配送状态"
                    size="small"
                    @change="handleSearch"
                >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="待接单" value="0"></el-option>
                    <el-option label="已接单" value="1"></el-option>
                    <el-option label="已到店" value="2"></el-option>
                    <el-option label="已取货" value="3"></el-option>
                    <el-option label="已送达" value="4"></el-option>
                    <el-option label="已完成" value="5"></el-option>
                    <el-option label="已退款" value="6"></el-option>
                </el-select>
            </div>
            <div class="right">
                <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-download"
                    @click="handleExport"
                >导出Excel</el-button>
            </div>
        </div>

        <el-table
            :data="tableData"
            border
            style="width: 100%"
            v-loading="loading"
        >
            <el-table-column
                label="序号"
                type="index"
                width="80"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="couriersName"
                label="配送员"
                width="120"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="shopName"
                label="所属商家"
                width="150"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="goodsInfo"
                label="商品信息"
                min-width="200"
                align="center"
            >
                <template slot-scope="scope">
                    <div class="goods-info">
                        <!-- <el-image
                            style="width: 50px; height: 50px"
                            :src="scope.row.goodsImage"
                            fit="cover"
                        ></el-image> -->
                        <div class="goods-detail">
                            <div>{{ scope.row.goodSpu }}</div>
                            <!-- <div class="goods-spec">{{ scope.row.goodsSpec }}</div> -->
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="订单号/下单时间"
                width="200"
                align="center"
            >
                <template slot-scope="scope">
                    <div>{{ scope.row.orderNo }}</div>
                    <div class="order-time">{{ scope.row.createOrderTime }}</div>
                </template>
            </el-table-column>
            <el-table-column
                label="商品总价/实付款"
                width="150"
                align="center"
            >
                <template slot-scope="scope">
                    <div>总价：¥{{ scope.row.salesPrice }}</div>
                    <div>实付：¥{{ scope.row.paymentPrice }}</div>
                </template>
            </el-table-column>
            <el-table-column
                prop="address"
                label="收货地址"
                min-width="200"
                align="center"
            ></el-table-column>
            <el-table-column
                prop="status"
                label="退款状态"
                width="100"
                align="center"
            >
                <template slot-scope="scope">
                    <el-tag :type="scope.row.status == '6' ? 'danger' : 'success'">
                        {{ scope.row.status == '6' ? '已退款' : '未退款' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column
                prop="paymentType"
                label="支付方式"
                width="100"
                align="center"
            >
                <template slot-scope="scope">
                    {{ scope.row.paymentType == '2' ? '支付宝' : '微信' }}
                </template>
            </el-table-column>
            <el-table-column
                prop="status"
                label="配送状态"
                width="100"
                align="center"
            >
                <template slot-scope="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                        {{ getStatusText(scope.row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            <!-- <el-table-column
                label="操作"
                width="120"
                align="center"
                fixed="right"
            >
                <template slot-scope="scope">
                    <el-button
                        v-if="scope.row.status === '1' || scope.row.status === '2'"
                        type="text"
                        size="small"
                        @click="handleCancelDelivery(scope.row)"
                    >取消配送</el-button>
                </template>
            </el-table-column> -->
        </el-table>

        <div class="pagination-container">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="page.current"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="page.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="page.total"
            >
            </el-pagination>
        </div>
    </el-dialog>
</template>

<script>
import * as XLSX from 'xlsx'
import { getRecords } from '@/api/mall/usercouriers'
export default {
    name: 'CouriersRecordsDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        courierId: {
            type: [String, Number],
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            tableData: [],
            filterForm: {
                status: ''
            },
            page: {
                total: 0,
                current: 1,
                size: 20
            }
        }
    },
    watch: {
        visible(val) {
            if (val) {
                this.getList()
            }
        }
    },
    methods: {
        handleClose() {
            this.$emit('update:visible', false)
        },
        handleSearch() {
            this.page.current = 1
            this.getList()
        },
        handleSizeChange(val) {
            this.page.size = val
            this.getList()
        },
        handleCurrentChange(val) {
            this.page.current = val
            this.getList()
        },
        handleExport() {
            // 准备导出数据
            const exportData = this.tableData.map((item, index) => ({
                '序号': index + 1,
                '配送员': item.couriersName,
                '所属商家': item.shopName,
                '商品名称': item.goodSpu,
                '订单号': item.orderNo,
                '下单时间': item.createOrderTime,
                '商品总价': `¥${item.salesPrice}`,
                '实付款': `¥${item.paymentPrice}`,
                '收货地址': item.address,
                '支付方式': item.paymentType == '2' ? '支付宝' : '微信',
                '配送状态': this.getStatusText(item.status),
            }))

            // 创建工作簿
            const wb = XLSX.utils.book_new()
            // 创建工作表
            const ws = XLSX.utils.json_to_sheet(exportData)

            // 设置列宽
            const colWidths = [
                { wch: 8 },  // ID
                { wch: 10 }, // 配送员
                { wch: 15 }, // 所属商家
                { wch: 20 }, // 商品名称
                { wch: 20 }, // 订单号
                { wch: 20 }, // 下单时间
                { wch: 12 }, // 商品总价
                { wch: 12 }, // 实付款
                { wch: 30 }, // 收货地址
                { wch: 10 }, // 支付方式
                { wch: 10 }, // 配送状态
            ]
            ws['!cols'] = colWidths

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, '配送记录')

            // 生成文件名
            const fileName = `配送记录_${new Date().toLocaleDateString()}.xlsx`

            // 导出文件
            XLSX.writeFile(wb, fileName)

            this.$message.success('导出成功')
        },
        handleCancelDelivery(row) {
            this.$confirm('确认取消该配送订单吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // TODO: 调用取消配送接口
                this.$message.success('取消成功')
                this.getList()
            }).catch(() => {})
        },
        getStatusType(status) {
            const statusMap = {
                '1': 'info',
                '2': 'warning',
                '3': 'primary',
                '4': 'success',
                '5': 'success',
                '6': 'danger'
            }
            return statusMap[status] || 'info'
        },
        getStatusText(status) {
            const statusMap = {
                '0': '待接单',
                '1': '已接单',
                '2': '已到店',
                '3': '已取货',
                '4': '已送达',
                '5': '已完成',
                '6': '已退款'
            }
            return statusMap[status] || '未知状态'
        },
        getList() {
            this.loading = true
            console.log(this.courierId, 'courierId');
            getRecords({
                id: this.courierId,
                current: this.page.current,
                size: this.page.size
            }).then(res => {
                console.log(res, 'res');
                this.tableData = res.data.data
                this.page.total = res.data.data.length
                this.loading = false
            })
            // TODO: 调用获取配送记录列表接口
            // 模拟数据
            // setTimeout(() => {
            //     this.tableData = [
            //         {
            //             id: 1,
            //             couriersName: '张三',
            //             shopName: '老王炸鸡店',
            //             goodSpu: '炸鸡套餐',
            //             orderNo: 'DD202403200001',
            //             createOrderTime: '2024-03-20 10:00:00',
            //             salesPrice: '88.00',
            //             paymentPrice: '88.00',
            //             address: '广东省深圳市南山区科技园',
            //             paymentType: '1',
            //             status: '1',
            //         },
            //         {
            //             id: 2,
            //             couriersName: '张三',
            //             shopName: '老李烧烤店',
            //             goodSpu: '烧烤套餐',
            //             orderNo: 'DD202403200002',
            //             createOrderTime: '2024-03-20 11:00:00',
            //             salesPrice: '128.00',
            //             paymentPrice: '128.00',
            //             address: '广东省深圳市福田区中心城',
            //             paymentType: '2',
            //             status: '2',
            //         }
            //     ]
            //     this.page.total = 100
            //     this.loading = false
            // }, 500)
        }
    }
}
</script>

<style lang="less" scoped>
.couriers-records-dialog {
    .filter-container {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        .left {
            display: flex;
            gap: 10px;
            align-items: center;
        }
    }

    .goods-info {
        display: flex;
        align-items: center;
        padding: 5px;
        .goods-detail {
            margin-left: 10px;
            text-align: left;
            .goods-spec {
                color: #909399;
                font-size: 12px;
                margin-top: 5px;
            }
        }
    }

    .order-time {
        color: #909399;
        font-size: 12px;
        margin-top: 5px;
    }

    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }
}
</style>
