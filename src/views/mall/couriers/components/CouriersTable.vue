<template>
    <div class="couriers-table">
        <el-table
            :data="tableData"
            border
            ref="multipleTable"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            v-loading="loading"
        >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
                fixed
                prop="id"
                label="ID"
                width="150"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="couriersName"
                label="配送员姓名"
                width="120"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="couriersPhone"
                label="配送员电话"
                width="120"
                align="center"
            >
            </el-table-column>
            <!-- <el-table-column
                prop="avatar"
                label="头像昵称"
                width="200"
                align="center"
            >
                <template slot-scope="scope">
                    <div class="avatar-container">
                        <el-avatar
                            :size="50"
                            :src="scope.row.avatar"
                        ></el-avatar>
                        <span>{{ scope.row.couriersName }}</span>
                    </div>
                </template>
            </el-table-column> -->
            <!-- <el-table-column
                prop="status"
                label="配送中/已完成"
                width="120"
                align="center"
            >
                <template slot-scope="scope"> 1/0 </template>
            </el-table-column> -->
            <el-table-column
                prop="createTime"
                label="创建时间"
                width="300"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="status"
                label="状态"
                width="120"
                align="center"
            >
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.status === '1'" type="success">已开启</el-tag>
                    <el-tag v-else type="danger">已关闭</el-tag>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        v-if="permissionList.editBtn"
                        @click="handleEdit(scope.row)"
                        type="text"
                        size="small"
                    >编辑</el-button>
                    <el-button
                        v-if="permissionList.delBtn"
                        @click="handleDelete(scope.row)"
                        type="text"
                        size="small"
                    >删除</el-button>
                    <el-button
                        v-if="permissionList.viewBtn"
                        @click="handleViewRecords(scope.row)"
                        type="text"
                        size="small"
                    >查看配送记录</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
export default {
    name: 'CouriersTable',
    props: {
        tableData: {
            type: Array,
            default: () => []
        },
        loading: {
            type: Boolean,
            default: false
        },
        permissionList: {
            type: Object,
            default: () => {}
        }
    },
    mounted () {
        this.$nextTick(() => {
            console.log(this.$refs.multipleTable, 'multipleTable');
            this.$refs.multipleTable.clearSelection()
        })
    },
    methods: {
        clearSelection () {
            this.$refs.multipleTable.clearSelection()
        },
        toggleRowSelection (row) {
            this.$refs.multipleTable.toggleRowSelection(row)
        },
        handleSelectionChange(val) {
            this.$emit('selection-change', val)
        },
        handleEdit(row) {
            this.$emit('edit', row)
        },
        handleDelete(row) {
            this.$emit('delete', row)
        },
        handleViewRecords(row) {
            console.log(row, 'row');
            this.$emit('view-records', row)
        }
    }
}
</script>

<style lang="less" scoped>
.couriers-table {
    /deep/ .el-table th > .cell {
        color: #000;
        font-weight: bold;
    }

    .avatar-container {
        display: flex;
        align-items: center;
        justify-content: center;
        span {
            margin-left: 10px;
        }
    }
}
</style>
