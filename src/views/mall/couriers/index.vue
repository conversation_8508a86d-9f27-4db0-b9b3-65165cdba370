<template>
    <div class="couriers-container">
        <div class="p-10 top">
            <div class="left">
                <el-button
                    type="primary"
                    size="small"
                    v-if="permissionList.addBtn"
                    @click="handleAdd"
                    >添加</el-button
                >
                <el-button
                    type="danger"
                    size="small"
                    v-if="permissionList.delBtn"
                    @click="handleBatchDelete"
                    >删除</el-button
                >
            </div>
            <div class="right">
                <div>
                    <span>姓名</span>
                    <el-input
                        v-model="searchName"
                        placeholder="请输入内容"
                        style="width: 200px"
                        size="small"
                        @keyup.enter.native="handleSearch"
                    ></el-input>
                </div>
                <div>
                    <span>状态</span>
                    <el-select
                        size="small"
                        v-model="status"
                        placeholder="请选择"
                        @change="handleSearch"
                    >
                        <el-option label="全部" value=""></el-option>
                        <el-option label="已开启" value="1"></el-option>
                        <el-option label="已关闭" value="0"></el-option>
                    </el-select>
                </div>
            </div>
        </div>
        <div class="p-10">
            <couriers-table
                :table-data="tableData"
                :loading="tableLoading"
                @selection-change="handleSelectionChange"
                @edit="handleEdit"
                ref="couriersTable"
                @delete="handleDelete"
                @view-records="handleViewRecords"
                :permissionList="permissionList"
            />
            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page.currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="page.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.total"
                >
                </el-pagination>
            </div>
        </div>

        <couriers-dialog
            :visible.sync="dialogVisible"
            :dialog-type="dialogType"
            :edit-data="editData"
            @submit="handleDialogSubmit"
            ref="couriersDialog"
        />

        <couriers-records-dialog
            :visible.sync="recordsDialogVisible"
            :courier-id="currentCourierId"
        />
    </div>
</template>

<script>
import CouriersTable from "./components/CouriersTable.vue";
import CouriersDialog from "./components/CouriersDialog.vue";
import CouriersRecordsDialog from "./components/CouriersRecordsDialog.vue";
import {
    getPage,
    getObj,
    addObj,
    putObj,
    delObj,
} from "@/api/mall/usercouriers";
import { mapGetters } from "vuex";

export default {
    name: "usercouriers",
    components: {
        CouriersTable,
        CouriersDialog,
        CouriersRecordsDialog,
    },
    props: {
        isSingle: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            tableData: [
                // {
                //     id: 1,
                //     couriersName: "张三",
                //     couriersPhone: "13800138000",
                //     avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
                //     status: "1",
                //     createTime: "2024-03-20 10:00:00",
                //     number: 1
                // },
                // {
                //     id: 2,
                //     couriersName: "李四",
                //     couriersPhone: "13800138001",
                //     avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
                //     status: "1",
                //     createTime: "2024-03-20 11:00:00",
                //     number: 2
                // },
                // {
                //     id: 3,
                //     couriersName: "王五",
                //     couriersPhone: "13800138002",
                //     avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
                //     status: "0",
                //     createTime: "2024-03-20 12:00:00",
                //     number: 3
                // },
                // {
                //     id: 4,
                //     couriersName: "赵六",
                //     couriersPhone: "13800138003",
                //     avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
                //     status: "1",
                //     createTime: "2024-03-20 13:00:00",
                //     number: 4
                // },
                // {
                //     id: 5,
                //     couriersName: "钱七",
                //     couriersPhone: "13800138004",
                //     avatar: "https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",
                //     status: "0",
                //     createTime: "2024-03-20 14:00:00",
                //     number: 5
                // }
            ],
            page: {
                total: 0,
                currentPage: 1,
                pageSize: 20,
                ascs: [],
                descs: [],
            },
            paramsSearch: {},
            tableLoading: false,
            searchName: "",
            status: "",
            dialogVisible: false,
            dialogType: "add",
            editData: {},
            selectionIds: [],
            recordsDialogVisible: false,
            currentCourierId: null,
        };
    },
    computed: {
        ...mapGetters(["permissions"]),
        permissionList() {
            return {
                addBtn: this.permissions["mall:couriers:add"] ? true : false,
                delBtn: this.permissions["mall:couriers:del"] ? true : false,
                editBtn: this.permissions["mall:couriers:edit"] ? true : false,
                viewBtn: this.permissions["mall:couriers:get"] ? true : false,
            };
        },
    },
    created() {
        this.getPage(this.page);
    },
    watch: {
        selectionIds: {
            handler(newVal) {
                console.log(newVal, "newVal");
                if (this.isSingle) {
                    // 单选模式
                    if (newVal.length > 1) {
                        const currentCourier = this.tableData.find(
                            (item) => newVal[0] == item.id
                        );
                        this.$refs.couriersTable.toggleRowSelection(
                            currentCourier
                        );
                    }
                }
            },
        },
        searchName: {
            handler (newVal) {
                if(newVal) return
                this.page.currentPage = 1;
                this.getPage(this.page);
            },
        },
    },
    methods: {
        clearSelection() {
            console.log("clearSelection");
            this.selectionIds = [];
            this.$refs.couriersTable.clearSelection();
        },
        handleSelectionChange(val) {
            this.selectionIds = val.map((item) => item.id);
        },
        handleAdd() {
            this.dialogType = "add";
            this.editData = {
                status: "1",
            };
            this.dialogVisible = true;
        },
        handleEdit(row) {
            this.dialogType = "edit";
            this.editData = { ...row };
            this.dialogVisible = true;
        },
        handleBatchDelete() {
            if (this.selectionIds.length === 0) {
                this.$message.warning("请选择要删除的数据");
                return;
            }
            this.$confirm("是否确认删除选中的数据", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    Promise.all(this.selectionIds.map((id) => delObj(id)))
                        .then(() => {
                            this.$message.success("删除成功");
                            this.getPage(this.page);
                        })
                        .catch(() => {
                            this.$message.error("删除失败");
                        });
                })
                .catch(() => {});
        },
        handleDelete(row) {
            this.$confirm("是否确认删除此数据", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
                .then(() => {
                    return delObj(row.id);
                })
                .then(() => {
                    this.$message.success("删除成功");
                    this.getPage(this.page);
                })
                .catch(() => {});
        },
        handleViewRecords(row) {
            this.currentCourierId = row.id;
            this.recordsDialogVisible = true;
        },
        handleSearch() {
            this.page.currentPage = 1;
            this.getPage(this.page);
        },
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.getPage(this.page);
        },
        handleCurrentChange(val) {
            this.page.currentPage = val;
            this.getPage(this.page);
        },
        handleDialogSubmit({ type, data }) {
            const request = type === "add" ? addObj : putObj;
            request(data)
                .then(() => {
                    this.$message.success(
                        `${type === "add" ? "添加" : "编辑"}成功`
                    );
                    this.dialogVisible = false;
                    this.getPage(this.page);
                })
                .catch(() => {
                    this.$message.error(
                        `${type === "add" ? "添加" : "编辑"}失败`
                    );
                })
                .finally(() => {
                    this.$refs.couriersDialog.loading = false;
                });
        },
        getPage(page, params) {
            this.tableLoading = true;
            const searchParams = {
                current: page.currentPage,
                size: page.pageSize,
                descs: this.page.descs,
                ascs: this.page.ascs,
                ...params,
                ...this.paramsSearch,
                couriersName: this.searchName,
                status: this.status,
            };

            getPage(searchParams)
                .then((response) => {
                    this.tableData = response.data.data.records;
                    this.page.total = response.data.data.total;
                    this.page.currentPage = page.currentPage;
                    this.page.pageSize = page.pageSize;
                })
                .catch(() => {
                    this.$message.error("获取数据失败");
                })
                .finally(() => {
                    this.tableLoading = false;
                });
        },
    },
};
</script>

<style lang="less" scoped>
.couriers-container {
    background-color: #fff;
    margin-top: 1px;
    padding: 10px;
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left {
            display: flex;
            align-items: center;
            span {
                margin-right: 15px;
            }
        }
        .right {
            display: flex;
            align-items: center;
            > div {
                margin-left: 30px;
            }
            span {
                margin-right: 15px;
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        text-align: right;
    }
}
</style>
