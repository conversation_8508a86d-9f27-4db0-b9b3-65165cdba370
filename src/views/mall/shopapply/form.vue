<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
  <div class="execution">
    <basic-container>
      <el-alert
        v-if="tenantInfo"
        style="font-weight: bold;"
        effect="dark"
        :title="'申请入驻【'+tenantInfo.name+'】'"
        type="success"
        :closable="false"
        center>
      </el-alert>
      <el-steps align-center :active="active" finish-status="success" style="padding: 20px">
        <el-step title="填写申请单"></el-step>
        <el-step title="审核申请单"></el-step>
        <el-step title="审核完成"></el-step>
      </el-steps>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>店铺入驻申请</span>
        </div>
        <avue-form v-model="form"
                   :option="type == 'view' ? formOption2 : formOption"
                   @submit="handleUpdate">
          <template slot="address" slot-scope="scope">
            <div>
              <i class="el-icon-location"></i>{{form.address}}
              <avue-input-map v-model="addressInfo" style="width:200px"></avue-input-map>
            </div>
          </template>
          <!-- 营业职照 -->
          <template slot="licenseUrl" slot-scope="scope">
            <div>
              <el-upload action="/upms/file/upload?fileType=image&dir=license/" class="avatar-uploader" :file-list="[]"
                :on-progress="handleProgress" :before-upload="beforeUpload" :on-success="handleSuccess1"
                :on-error="handleError">
                <img v-if="form.licenseUrl" :src="form.licenseUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
             </el-upload>
            </div>
          </template>
          <!-- 身份证正面 -->
          <template slot="idCardFrontUrl" slot-scope="scope">
              <el-upload action="/upms/file/upload?fileType=image&dir=license/" class="avatar-uploader" :file-list="[]"
                :on-progress="handleProgress" :before-upload="beforeUpload" :on-success="handleSuccess2"
                :on-error="handleError">
                <img v-if="form.idCardFrontUrl" :src="form.idCardFrontUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
          </template>
          <!-- 身份证反面 -->
          <template slot="idCardBackUrl" slot-scope="scope">
              <el-upload action="/upms/file/upload?fileType=image&dir=license/" class="avatar-uploader" :file-list="[]"
                :on-progress="handleProgress" :before-upload="beforeUpload" :on-success="handleSuccess3"
                :on-error="handleError">
                <img v-if="form.idCardBackUrl" :src="form.idCardBackUrl" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
          </template>
        </avue-form>
      </el-card>
      <div style="padding: 10px;text-align: center">
        <a style="cursor: pointer;color: #0a6aff;"  @click="toView">申请单查询</a>
       <div  class="jl-tips-class" v-if="applyLogId">
         申请记录：<span style="cursor: pointer;" class="jl-tips-class" @click="queryView(applyLogId)">{{applyLogId}}</span>
       </div>
      </div>
    </basic-container>
  </div>
</template>

<script>
  import {getStore, setStore} from '@/util/store'
  import {getPage, getObj, addObj, putObj, delObj, getOne} from '@/api/mall/shopapply'
  import {getObjOutSide as getTenant} from '@/api/upms/tenant'
  import { formOption, formOption2 } from '@/const/crud/mall/shopapply'


  export default {
    name: 'shopapply-form',
    components: {
    },
    data() {
      return {
        form: {},
        formOption: formOption,
        formOption2: formOption2,
        addressInfo: [],
        type: '',
        id: '',
        active: 0,
        tenantInfo: null,
        applyLogId: ''
      }
    },
    created() {
      this.form.tenantId = this.$route.query.tenant_id
      this.type = this.$route.query.type
      this.id = this.$route.query.id
      window.tenantId = this.$route.query.tenant_id
      this.getTenant()
      if(this.type == 'edit' || this.type == 'view'){
        this.active = 1
        this.getOne()
      }

      let applyLogId = getStore({ name: 'applyLogId'+this.form.tenantId })
      if(applyLogId){
        this.applyLogId = applyLogId
      }
    },
    watch: {
      addressInfo: {
        deep: true,
        immediate: true,
        handler(newSkus, oldSkus) {
          this.form.address = newSkus[2]
        }
      }
    },
    computed: {},
    methods: {
        beforeUpload (file) {
            console.log(file)
            const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/gif';
            const isLt2M = file.size / 1024 / 1024 < 20;

            if (!isJPG) {
                this.$message.error('上传图片格式不正确!');
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 20MB!');
            }
            return isJPG && isLt2M;
        },
        handleError (err, file, fileList) {
            this.$message.error('上传失败')
        },
        handleSuccess1 (res, file) {
            console.log(res, file)
            this.form.licenseUrl = res.data.url
            this.$message.success('上传成功')
        },
        handleSuccess2 (res, file) {
            this.form.idCardFrontUrl = res.data.url
            this.$message.success('上传成功')
        },
        handleSuccess3 (res, file) {
            this.form.idCardBackUrl = res.data.url
            this.$message.success('上传成功')
        },


      toView(){
        this.$prompt('请输入申请单编号', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /\S/,
          inputErrorMessage: '申请单编号格式不正确'
        }).then(({ value }) => {
          this.queryView(value)
        }).catch(() => {

        });
      },
      queryView(value){
        window.location.href = '#/mall/shop/shopapply/form?tenant_id='+this.form.tenantId+'&type=view&id='+value
        location.reload()
      },
      getTenant(){
        getTenant(this.form.tenantId).then(response => {
          this.tenantInfo = response.data.data
        })
      },
      getOne(){
        getOne({
          tenantId: this.form.tenantId,
          id: this.id
        }).then(response => {
          if(response.data.data){
            this.form = response.data.data
            this.addressInfo = [this.form.longitude, this.form.latitude, this.form.address]
            if(this.form.status != '0'){
              this.active = 3
            }
          }else{
            this.$message({
              showClose: true,
              message: '无此申请单',
              type: 'error'
            })
          }
        }).catch(() => {
        })
      },
      handleUpdate(form, done) {
        let addressInfo = this.addressInfo
        form.longitude = addressInfo[0]
        form.latitude = addressInfo[1]
        form.address = addressInfo[2]
        form.licenseUrl = this.form.licenseUrl
        form.idCardFrontUrlUrl = this.form.idCardFrontUrl
        form.idCardBackUrlUrl = this.form.idCardBackUrl
        putObj(form).then(response => {
          setStore({ name: 'applyLogId'+this.form.tenantId, content: response.data.data.id })
          window.location.href = '#/mall/shop/shopapply/form?tenant_id='+this.form.tenantId+'&type=view&id='+response.data.data.id
          location.reload()
        }).catch(() => {
          done()
        })
      },
    }
  }
</script>

<style lang="scss">
  /*#app {*/
  /*  height: unset!important*/
  /*}*/
  /*#html, body, #app {*/
  /*  height: unset!important*/
  /*}*/
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
