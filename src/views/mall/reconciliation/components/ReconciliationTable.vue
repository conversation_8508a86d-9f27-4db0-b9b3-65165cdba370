<template>
  <div class="reconciliation-table">
    <div class="table-header">
      <div class="statistics">
        <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
                <el-tag type="success">商品订单总数 （{{ orderTotalNum }}）</el-tag>
            </el-form-item>
            <el-form-item >
                <el-tag type="warning">完成订单金额 （{{ orderSumPrice }}¥）</el-tag>
            </el-form-item>
            <el-form-item >
                <el-tag type="info">退款个数 （{{ orderBackNum }}）</el-tag>
            </el-form-item>
            <el-form-item >
                <el-tag type="danger">退款金额 （{{ orderBackSumPrice }}¥）</el-tag>
            </el-form-item>
            <el-form-item >
                <el-tag>总计金额（{{ totalPrice }}¥）</el-tag>
            </el-form-item>
        </el-form>
      </div>
      <div class="operations">
        <el-button size="small" icon="el-icon-download" type="primary" @click="handleExport">导出</el-button>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      style="width: 100%">
      <!-- <el-table-column
        type="selection"
        width="55">
      </el-table-column> -->
      <el-table-column
        prop="shopName"
        label="店铺"
        align="center"
        width="180">
      </el-table-column>
      <el-table-column
        prop="orderId"
        label="订单ID"
        align="center"
        width="180">
      </el-table-column>
      <el-table-column
        prop="status"
        align="center"
        label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '0'" type="">正常</el-tag>
          <el-tag v-if="scope.row.status == '1'" type="warning">退款中</el-tag>
          <el-tag v-if="scope.row.status == '2'" type="warning">退货退款中</el-tag>
          <el-tag v-if="scope.row.status == '3'" type="success">退款成功</el-tag>
          <el-tag v-if="scope.row.status == '4'" type="success">完成退货退款</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="amount"
        align="center"
        sortable
        label="金额">
        <template slot-scope="scope">
          {{ scope.row.paymentPrice.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="spuName"
        align="center"
        sortable
        label="商品名称">
      </el-table-column>
      <el-table-column
        prop="createTime"
        align="center"
        sortable
        label="创建时间">
      </el-table-column>
      <!-- <el-table-column
        label="操作"
        align="center"
        width="100">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 30, 50,100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <!-- 使用抽离的详情抽屉组件 -->
    <order-detail-drawer
      :visible.sync="drawerVisible"
      :detail="currentRow"
      @close="handleDrawerClose"
    />
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import OrderDetailDrawer from './OrderDetailDrawer.vue'
import { staticOrder } from '@/api/mall/orderinfo'

export default {
  name: 'ReconciliationTable',
  components: {
    OrderDetailDrawer
  },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      total: 0,
      orderBackSumPrice: 0,//退款订单金额
      orderBackNum: 0,//退款订单量
      orderSumPrice: 0,//已完成的订单金额
      orderTotalNum: 0,//已完成的订单量
      totalPrice: 0,//总金额
      drawerVisible: false,
      currentRow: null,
      sortConfig: {
        prop: '',
        order: ''
      },
      tableData: [],
      searchForm: {
        shopId: '',
        orderId: '',
        goodsId: '',
        status: '',
        startTime: '',
        endTime: ''
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableData(this.searchForm)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTableData(this.searchForm)
    },
    handleView(row) {
      this.currentRow = row
      this.drawerVisible = true
    },
    handleDrawerClose() {
      this.currentRow = null
    },
    // 处理排序变化 - @sort-change="handleSortChange"
    handleSortChange({ prop, order }) {
    },
    // 获取表格数据（这里先用模拟数据，实际项目中应该调用接口）
    getTableData (params) {
      this.searchForm = params
      staticOrder({
        current: this.currentPage,
        size: this.pageSize,
        shopId: params && params.shopId,
        orderId: params && params.orderId,
        spuId: params && params.goodsId,
        status: params && params.status,
        startTime: params && String(params.startTime),
        endTime: params && String(params.endTime)
      }).then(res => {
        console.log(res.data.data)
        this.tableData = res.data.data.page.records
        this.total = res.data.data.page.total
        this.orderTotalNum = res.data.data.orderTotalNum
        this.orderSumPrice = res.data.data.orderSumPrice
        this.orderBackNum = res.data.data.orderBackNum
        this.orderBackSumPrice = res.data.data.orderBackSumPrice
        this.totalPrice = res.data.data.totalPrice
      })
    },
    async handleExport() {
      try {
        // 这里应该调用接口获取所有数据，现在用模拟数据
        const allData = [...this.tableData]

        // 表头
        const headers = ['店铺', '订单ID', '状态', '金额(元)', '商品名称', '创建时间']

        // 处理数据
        const excelData = allData.map(item => [
          item.shopName,
          item.orderId,
          item.status == '0' ? '正常' : item.status == '1' ? '退款中' : item.status == '2' ? '退货退款中' : item.status == '3' ? '退款成功' : item.status == '4' ? '完成退货退款' : item.status,
          Number(item.paymentPrice).toFixed(2),
          item.spuName,
          item.createTime,
        ])

        // excelData.push([
        //   '',
        //   '',
        //   '',
        //   '',
        //   '',
        // ])

        // // 添加总计行-右对齐
        // excelData.push([
        //   '总计',
        //   this.totalPrice.toFixed(2) + '元',
        //   '',
        //   '',
        //   ''
        // ])
        // // 商品订单数
        // excelData.push([
        //   '商品订单数',
        //   this.orderTotalNum + '个',
        //   '',
        //   '',
        //   ''
        // ])
        // // 订单金额
        // excelData.push([
        //   '订单金额',
        //   this.orderSumPrice.toFixed(2) + '元',
        //   '',
        //   '',
        //   ''
        // ])
        // // 退款个数
        // excelData.push([
        //   '退款个数',
        //   this.orderBackNum + '个',
        //   '',
        //   '',
        //   ''
        // ])
        // // 退款金额
        // excelData.push([
        //   '退款金额',
        //   this.orderBackSumPrice.toFixed(2) + '元',
        //   '',
        //   '',
        //   ''
        // ])

        // 合并数据
        const data = [headers, ...excelData]

        // 创建工作表
        const ws = XLSX.utils.aoa_to_sheet(data)

        // 设置列宽
        ws['!cols'] = [
          { wch: 15 }, // 店铺
          { wch: 20 }, // 订单编号
          { wch: 10 }, // 状态
          { wch: 12 }, // 金额
          { wch: 40 }, // 商品名称
          { wch: 20 }, // 创建时间
        ]

        // 创建工作簿
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, '对账单')

        // 生成文件名（包含当前日期）
        const date = new Date()
        const fileName = `对账单_${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}.xlsx`

        // 导出文件
        XLSX.writeFile(wb, fileName)

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败：', error)
        this.$message.error('导出失败，请重试')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.reconciliation-table {
  margin-top: 20px;
}
.table-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  .operations{
    margin-top: 3px;
  }
}
.statistics {
  display: flex;
  gap: 10px;
}
.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
