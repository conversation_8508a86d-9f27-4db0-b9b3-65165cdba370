<template>
  <el-drawer
    title="订单详情"
    :visible="isVisible"
    :direction="direction"
    size="50%"
    :before-close="handleClose">
    <div class="drawer-content" v-if="detail">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="店铺">{{ detail.shop }}</el-descriptions-item>
        <el-descriptions-item label="订单编号">{{ detail.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="detail.status == '已完成'" type="success">已完成</el-tag>
          <el-tag v-if="detail.status == '已退款'" type="danger">已退款</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="金额">{{ detail.amount.toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ detail.payTime }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="出单员">{{ detail.creator }}</el-descriptions-item>
      </el-descriptions>

      <div class="detail-section">
        <h3>商品信息</h3>
        <el-table :data="products" border size="small">
          <el-table-column prop="name" label="商品名称"></el-table-column>
          <el-table-column prop="price" label="单价">
            <template slot-scope="scope">
              {{ scope.row.price.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量"></el-table-column>
          <el-table-column label="小计">
            <template slot-scope="scope">
              {{ (scope.row.price * scope.row.quantity).toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'OrderDetailDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: null
    }
  },
  computed: {
    isVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  data() {
    return {
      direction: 'rtl',
      // 模拟商品数据，实际应该通过props传入或在组件内根据订单ID请求
      products: [
        {
          name: '测试商品1',
          price: 50,
          quantity: 1
        },
        {
          name: '测试商品2',
          price: 25,
          quantity: 2
        }
      ]
    }
  },
  methods: {
    handleClose(done) {
        this.isVisible = false
        this.$emit('close')
    }
  }
}
</script>

<style scoped>
.drawer-content {
  padding: 20px;
}
.detail-section {
  margin-top: 30px;
}
.detail-section h3 {
  margin-bottom: 15px;
  font-size: 16px;
  color: #303133;
}
</style>
