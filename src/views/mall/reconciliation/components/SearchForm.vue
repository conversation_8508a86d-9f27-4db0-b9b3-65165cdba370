<template>
  <div class="search-form">
    <el-form :inline="true" :model="formData" class="demo-form-inline">
      <el-form-item label="选择店铺">
        <el-select
          size="small"
          v-model="formData.shopId"
          placeholder="请选择店铺"
          clearable
          filterable
          @change="handleShopChange">
          <el-option
            v-for="item in shopList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择商品">
        <el-select
          size="small"
          v-model="formData.goodsId"
          placeholder="请选择商品"
          clearable
          filterable
          remote
          :remote-method="handleProductSearch"
          :loading="productLoading"
          :disabled="!formData.shopId">
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="item.name"
            :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态">
        <el-select size="small" v-model="formData.status" placeholder="请选择状态" clearable>
          <!-- <el-option label="正常" value="0"></el-option> -->
          <el-option label="退款中" value="1"></el-option>
          <el-option label="退货退款中" value="2"></el-option>
          <el-option label="完成退款" value="3"></el-option>
          <el-option label="完成退货退款" value="4"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单ID">
        <el-input
          size="small"
          v-model="formData.orderId"
          placeholder="请输入订单ID"
          clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          size="small"
          v-model="formData.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="handleSearch">查询</el-button>
        <el-button size="small" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getList as getShopList } from '@/api/mall/shopinfo'
import { getPage as getProductList } from '@/api/mall/goodsspu'

export default {
  name: 'SearchForm',
  data() {
    return {
      formData: {
        orderId: '',
        shopId: '',
        goodsId: '',
        status: '',//状态0：正常；1：退款中；2：退货退款中；3：完成退款；4：完成退货退款
        timeRange: [], // 时间范围
        startTime: '', // 开始时间
        endTime: '' // 结束时间
      },
      shopList: [],
      productList: [],
      productLoading: false,
      searchTimeout: null,
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const start = new Date()
            start.setHours(0, 0, 0, 0)
            const end = new Date()
            end.setHours(23, 59, 59, 999)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24)
            start.setHours(0, 0, 0, 0)
            const end = new Date()
            end.setTime(end.getTime() - 3600 * 1000 * 24)
            end.setHours(23, 59, 59, 999)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            end.setHours(23, 59, 59, 999);
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            end.setHours(23, 59, 59, 999);
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            end.setHours(23, 59, 59, 999);
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            start.setHours(0, 0, 0, 0);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    }
  },
  created() {
    this.getShopList()
  },
  methods: {
    handleSearch() {
      if (!this.formData.shopId && !this.formData.goodsId && !this.formData.status && !this.formData.orderId && !this.formData.timeRange) {
        this.$message.warning('请选择查询条件！')
        return
      }
      // 处理时间范围
      if (this.formData.timeRange && this.formData.timeRange.length === 2) {
        this.formData.startTime = this.formData.timeRange[0]
        this.formData.endTime = this.formData.timeRange[1]
      } else {
        this.formData.startTime = ''
        this.formData.endTime = ''
      }
      this.$emit('search', this.formData)
    },
    handleReset() {
      this.formData = {
        orderId: '',
        shopId: '',
        goodsId: '',
        status: '',
        timeRange: [],
        startTime: '',
        endTime: ''
      }
      this.productList = []
      this.$emit('reset')
    },
    // 获取店铺列表
    async getShopList() {
      try {
        const res = await getShopList({})
        if (res.data.data) {
          this.shopList = res.data.data
        }
      } catch (error) {
        console.error('获取店铺列表失败：', error)
        this.$message.error('获取店铺列表失败')
      }
    },
    // 店铺变化时获取商品列表
    async handleShopChange(value) {
      this.formData.goodsId = ''
      this.productList = []
      if (!value) return

      await this.loadProducts()
    },
    // 加载商品列表
    async loadProducts(query = '') {
      this.productLoading = true
      try {
        const res = await getProductList({
          shopId: this.formData.shopId,
          current: 1,
          size: 100,
          name: query // 添加商品名称搜索
        })
        if (res.data.data && res.data.data.records) {
          this.productList = res.data.data.records
        }
      } catch (error) {
        console.error('获取商品列表失败：', error)
        this.$message.error('获取商品列表失败')
      } finally {
        this.productLoading = false
      }
    },
    // 商品远程搜索
    handleProductSearch(query) {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout)
      }
      if (!query) {
        this.loadProducts()
        return
      }

      this.searchTimeout = setTimeout(() => {
        this.loadProducts(query)
      }, 300)
    }
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}
</style>
