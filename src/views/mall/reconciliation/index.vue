<template>
  <div class="reconciliation-page">
    <search-form
      @search="handleSearch"
      @reset="handleReset"
    />
    <reconciliation-table
      ref="table"
      @view="handleView"
      @hook:mounted="handleMounted"
    />
  </div>
</template>

<script>
import SearchForm from './components/SearchForm.vue'
import ReconciliationTable from './components/ReconciliationTable.vue'

export default {
  name: 'Reconciliation',
  components: {
    SearchForm,
    ReconciliationTable
  },
  methods: {
    handleSearch(formData) {
      console.log('搜索条件：', formData)
      // 这里可以调用接口获取数据
      this.$refs.table.getTableData(formData)
    },
    handleReset() {
      // 重置表格数据
      this.$refs.table.currentPage = 1
      this.handleSearch()
    },
    handleView(row) {
      console.log('查看详情：', row)
      // 这里可以处理查看详情逻辑
    },
    handleMounted() {
      this.handleSearch()
    }
  }
}
</script>

<style scoped>
.reconciliation-page {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
}
</style>
