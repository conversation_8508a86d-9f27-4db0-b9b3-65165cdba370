<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
    <div class="execution">
        <basic-container>
            <avue-crud ref="crud"
                       :page.sync="page"
                       :data="tableData"
                       :permission="permissionList"
                       :table-loading="tableLoading"
                       :option="tableOption"
                       v-model="form"
                       @on-load="getPage"
                       @refresh-change="refreshChange"
                       @row-update="handleUpdate"
                       @row-save="handleSave"
                       @row-del="handleDel"
                       @sort-change="sortChange"
                       @search-change="searchChange">
                <template slot-scope="scope" slot="status">
                    <el-tag type="warning" @click="handleOrderItemStatus(scope.row)" style="cursor: pointer"><i
                        class="el-icon-edit"></i>{{ scope.row.statusDesc }}
                    </el-tag>
                </template>
                <template slot-scope="scope" slot="isRefund" v-if="scope.row.orderItem">
                    <el-tag type="success" v-if="scope.row.orderItem.isRefund == '1'">是</el-tag>
                    <el-tag type="danger" v-if="scope.row.orderItem.isRefund == '0'">否</el-tag>
                </template>
                <template slot-scope="scope" slot="refundAmount">
                    <div>
                        <span class="jl-money-text">{{ scope.row.refundAmount }}</span>
                    </div>
                </template>
                <template slot-scope="scope" slot="orderId" v-if="scope.row.orderInfo">
                    <div style="text-align: left">
                        <div class="grid-content ">订单编号：{{ scope.row.orderInfo.orderNo }}</div>
                        <div v-if="scope.row.orderItem">
                            <div class="grid-content">销售金额：<span
                                class="jl-money-text">{{ scope.row.orderItem.salesPrice }}</span>
                            </div>
                            <div class="grid-content">运费金额：<span class="jl-money-text">{{
                                    scope.row.orderItem.freightPrice
                                }}</span>
                            </div>
                            <div class="grid-content">支付金额：<span class="jl-money-text">{{
                                    scope.row.orderItem.paymentPrice
                                }}</span></div>
                            <div class="grid-content">支付积分：{{ scope.row.orderItem.paymentPoints }}</div>
                        </div>
                    </div>
                </template>
                <template slot-scope="scope" slot="orderItemId" v-if="scope.row.orderItem">
                    <div class="jl-flex" style="text-align: left">
                        <div>
                            <img :src="scope.row.orderItem.picUrl" class="jl-img-width">
                        </div>
                        <div class="jl-flex-sub ">
                            <div class="jl-text-line2 " :title="scope.row.orderItem.spuName">
                                {{ scope.row.orderItem.spuName }}
                            </div>
                            <div class="jl-tips-class">{{ scope.row.orderItem.specInfo }}</div>
                        </div>
                    </div>
                </template>
            </avue-crud>
            <el-dialog
                title="退款管理"
                :visible.sync="dialogRefunds"
                destroy-on-close
                top="20px"
                width="40%">
                <el-card shadow="never" style="margin-top: -20px">
                    <div slot="header">
                        <span>退款信息</span>
                    </div>
                    <el-row :gutter="10"
                            style="border:1px solid #eaeaea;padding: 5px">
                        <el-col :span="3">
                            <img :src="orderItemObj.picUrl" width="100%"/>
                        </el-col>
                        <el-col :span="13" style="text-align: left">
                            <div class="spu-name">{{ orderItemObj.spuName }}</div>
                            <div class="spec-info">{{ orderItemObj.specInfo }}</div>
                        </el-col>
                        <el-col :span="8">
                            <div class="grid-content">￥{{ orderItemObj.salesPrice }}</div>
                            <div class="grid-content">×{{ orderItemObj.quantity }}件</div>
                        </el-col>
                    </el-row>
                    <div style="padding-left: 20px">
                        <el-row>
                            <el-col :span="12">
                                <div style="margin-top: 20px">
                                    退款状态：
                                    <el-tag type="danger" size="small" v-if="orderItemObj.listOrderRefunds">
                                        {{ orderItemObj.listOrderRefunds[0].statusDesc }}
                                    </el-tag>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="margin-top: 20px">
                                    是否退款：
                                    <el-tag :type="orderItemObj.isRefund == '1' ? 'success' : 'danger'" size="small">
                                        {{ orderItemObj.isRefund == '1' ? '是' : '否' }}
                                    </el-tag>
                                    <el-tooltip style="margin-left: 10px" effect="dark"
                                                content="“是”则代表退款金额已成功到达用户账上"
                                                placement="top-start">
                                        <i class="el-icon-info"></i>
                                    </el-tooltip>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="margin-top: 20px">
                                    订单金额：￥{{ (orderItemObj.quantity * orderItemObj.salesPrice).toFixed(2) }}
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="margin-top: 20px;">
                                    商品数量：x{{ orderItemObj.quantity }}
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="margin-top: 20px">
                                    支付金额：￥{{ orderItemObj.paymentPrice }}
                                </div>
                            </el-col>
                            <el-col :span="12">

                                <div style="margin-top: 20px">
                                    运费金额：￥{{ orderItemObj.freightPrice }}
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="margin-top: 20px; color: red" v-if="orderItemObj.listOrderRefunds">
                                    <div>
                                        退款金额：
                                        <!-- 退款金额修改暂时不支持(后台金额退款有问题) -->
                                        <el-input-number
                                            v-if="orderRefundsStatusList.length > 0"
                                            v-model="orderItemObj.listOrderRefunds[0].refundAmount"
                                            :precision="2"
                                            :step="0.1"
                                            :min="0"
                                            :max="orderItemObj.paymentPrice"
                                            size="small"
                                            />
                                        <span v-else>￥{{
                                            orderItemObj.listOrderRefunds[0].refundAmount
                                        }}</span>
                                        <el-tooltip v-if="orderRefundsStatusList.length > 0" style="margin-left: 10px" effect="dark"
                                        :content="'退款金额不能超过支付金额：'+orderItemObj.paymentPrice"
                                        placement="top-start">
                                        <i class="el-icon-info"></i>
                                        </el-tooltip>
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="margin-top: 20px; color: red">
                                    退款积分：{{ orderItemObj.paymentPoints }}
                                </div>
                            </el-col>
                        </el-row>
                        <div style="margin-top: 20px" v-if="orderItemObj.listOrderRefunds">
                            退款原因：{{ orderItemObj.listOrderRefunds[0].refundReson || '-' }}
                        </div>
                        <div style="margin-top: 20px" v-if="orderItemObj.listOrderRefunds">
                            拒绝原因：{{ orderItemObj.listOrderRefunds[0].refuseRefundReson || '-' }}
                        </div>
                    </div>
                    <div
                        v-if="
              orderItemObj.listOrderRefunds &&
              orderItemObj.listOrderRefunds.length &&
              (orderItemObj.listOrderRefunds[0].status == '21' ||
                orderItemObj.listOrderRefunds[0].status == '211' ||
                orderItemObj.listOrderRefunds[0].status == '212')
            "
                        class="mt-[10px] ml-[-5px]"
                    >
                        <div style="font-weight: bold;margin: 20px 0 5px 0">
                            退货信息
                        </div>
                        <div style="padding-left: 20px;">
                            <el-row style="display: flex;align-items: center">
                                <el-col :span="12">退货快递: {{
                                        getLogisticsName(
                                            orderItemObj.listOrderRefunds[0].logisticsCode
                                        )
                                    }}
                                </el-col>
                                <el-col :span="12">快递单号:
                                    <el-tooltip
                                        effect="dark"
                                        content="注意: 手动查询物流信息需要充值费用."
                                        placement="top"
                                    >
                                        <el-button
                                            type="text"
                                            icon="el-icon-view"
                                            @click="
                        handleLogisticsInfo(
                          orderItemObj.listOrderRefunds[0],
                          orderRefunds.receiveInfo.phone
                        )
                      "
                                        >{{
                                                orderItemObj.listOrderRefunds[0].logisticsNo || "-"
                                            }}
                                        </el-button
                                        >
                                    </el-tooltip>
                                </el-col>
                            </el-row>

                            <el-row style="color: #999">
                                <el-col :span="12">收件昵称: {{ orderRefunds.receiveInfo.nickName }}
                                </el-col>
                                <el-col :span="12">收件电话: {{ orderRefunds.receiveInfo.phone }}
                                </el-col>
                                <el-col :span="24" style="margin-top: 10px">收件地址:
                                    {{ orderRefunds.receiveInfo.address }}
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                </el-card>
                <el-card shadow="never" style="margin-top: 10px" v-if="orderRefundsStatusList.length > 0">
                    <div slot="header">
                        <span>退款操作</span>
                    </div>
                    <el-form ref="orderRefunds" :model="orderRefunds" :rules="orderRefundsRules" label-width="80px">
                        <el-form-item label="退款状态" prop="status">
                            <el-select v-model="orderRefunds.status" @change="orderRefundsChange" placeholder="请选择">
                                <el-option
                                    v-for="item in orderRefundsStatusList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="拒绝原因" prop="refuseRefundReson"
                                      v-if="orderRefunds.status == '12' || orderRefunds.status == '22' || orderRefunds.status == '212'">
                            <el-input type="textarea" maxlength="200"
                                      show-word-limit v-model="orderRefunds.refuseRefundReson"
                                      placeholder="请填写拒绝原因"></el-input>
                        </el-form-item>
                        <div v-if="orderRefunds.status == '21'&&orderRefunds.receiveInfo">
                            <el-form-item
                                v-if="orderRefunds.status == '21'"
                                label="收件人"
                                prop="receiveInfo.nickName"
                            >
                                <el-input
                                    v-model="orderRefunds.receiveInfo.nickName"
                                    maxlength="50"
                                    placeholder="退货收件人"
                                />
                            </el-form-item>
                            <el-form-item
                                v-if="orderRefunds.status == '21'"
                                label="收件电话"
                                prop="receiveInfo.phone"
                            >
                                <el-input
                                    v-model="orderRefunds.receiveInfo.phone"
                                    maxlength="50"
                                    placeholder="退货收件电话"
                                />
                            </el-form-item>
                            <el-form-item
                                v-if="orderRefunds.status == '21'"
                                label="收件地址"
                                prop="receiveInfo.address"
                            >
                                <el-input
                                    v-model="orderRefunds.receiveInfo.address"
                                    type="textarea"
                                    maxlength="300"
                                    show-word-limit
                                    placeholder="退货收件地址"
                                />
                            </el-form-item>
                        </div>
                        <div style="text-align: center">
                            <el-button type="primary" icon="el-icon-check" v-loading="orderRefundsSubmitLoading"
                                       @click="orderRefundsSubmit(orderItemObj.listOrderRefunds[0])">确认提交
                            </el-button>
                        </div>
                    </el-form>
                </el-card>
            </el-dialog>
        </basic-container>
        <el-dialog
            :visible.sync="logisticsInfo.dialog"
            title="物流信息"
            width="500px"
            top="20px"
        >
            <div style="margin-top: -20px">
                <div>更新时间:{{
                        logisticsInfo.updateTime
                    }}
                    <el-button icon="el-icon-refresh" type="text" @click="refreshLogisticsInfoData"/>
                </div>
                <div style="max-height: 70vh;overflow:auto;">
                    <el-steps direction="vertical" :active="0" class="mt-2">
                        <el-step
                            v-for="(item, index) in logisticsInfo.data.data"
                            :key="index"
                            :title="item.time"
                            :description="item.context"
                            icon="el-icon-truck"
                        />
                    </el-steps>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {getPage, getObj, addObj, putObj, delObj} from '@/api/mall/orderrefunds'
import {tableOption} from '@/const/crud/mall/orderrefunds'
import {getObj as getOrderItem} from '@/api/mall/orderitem'
import {doOrderRefunds} from '@/api/mall/orderrefunds'
import {mapGetters} from 'vuex'
import {getLogisticsInfo} from "@/api/mall/logistics";
import {getObj as getShopInfo} from "@/api/mall/shopinfo";
import {getStore, removeStore, setStore} from '@/util/store'
import {dicSelect as logisticsDic} from "@/const/crud/mall/logistics";

export default {
    name: 'orderrefunds',
    data() {
        return {
            form: {},
            tableData: [],
            page: {
                total: 0, // 总页数
                currentPage: 1, // 当前页数
                pageSize: 20, // 每页显示多少条
                ascs: [],//升序字段
                descs: 'create_time'//降序字段
            },
            paramsSearch: {},
            tableLoading: false,
            tableOption: tableOption,
            dialogRefunds: false,
            orderRefunds: {
                id: "",
                status: "",
                refundAmount: 0,
                refuseRefundReson: "",
                receiveInfo: {
                    nickName: "",
                    phone: "",
                    address: ""
                }
            },
            orderItemObj: {},
            orderRefundsStatusList: [],
            orderRefundsRules: {
                status: [
                    {required: true, message: '请选择退款状态', trigger: 'change'}
                ],
                refuseRefundReson: [
                    {
                        required: true,
                        message: "不能为空",
                        trigger: "blur"
                    }
                ],
                "receiveInfo.nickName": [
                    {
                        required: true,
                        message: "不能为空",
                        trigger: "blur"
                    }
                ],
                "receiveInfo.phone": [
                    {
                        required: true,
                        message: "不能为空",
                        trigger: "blur"
                    }
                ],
                "receiveInfo.address": [
                    {
                        required: true,
                        message: "不能为空",
                        trigger: "blur"
                    }
                ]
            },
            orderRefundsSubmitLoading: false,
            logisticsInfo: {
                dialog: false,
                data: {},
                updateTime: "",
                params: {}
            },
            logisticsDic, //快递字典
        }
    },
    created() {
    },
    mounted: function () {
    },
    computed: {
        ...mapGetters(['permissions', 'roles']),
        permissionList() {
            return {
                addBtn: this.permissions['mall:orderrefunds:add'] ? true : false,
                delBtn: this.permissions['mall:orderrefunds:del'] ? true : false,
                editBtn: this.permissions['mall:orderrefunds:edit'] ? true : false,
                viewBtn: this.permissions['mall:orderrefunds:get'] ? true : false
            };
        }
    },
    methods: {
        orderRefundsSubmit (orderRefunds) {
            this.orderRefunds.id = orderRefunds.id
            this.orderRefunds.refundAmount = orderRefunds.refundAmount;//退款金额
            this.$refs['orderRefunds'].validate((valid) => {
                if (valid) {
                    console.log(this.orderRefunds, "this.orderRefunds");
                    // return
                    this.orderRefundsSubmitLoading = true
                    doOrderRefunds(this.orderRefunds).then(res => {
                        this.orderRefundsSubmitLoading = false
                        this.dialogRefunds = false
                        this.$message.success('退款成功！')
                        this.getPage(this.page)
                    }).catch(() => {
                        this.orderRefundsSubmitLoading = false
                    })
                } else {
                    return false
                }
            })
        },
        handleOrderItemStatus(obj) {
            this.dialogRefunds = true
            this.orderItemObj = {}
            this.orderRefunds = {
                id: "",
                status: "",
                refundAmount: 0,
                refuseRefundReson: "",
                receiveInfo: {
                    nickName: "",
                    phone: "",
                    address: ""
                }
            }
            getOrderItem(obj.orderItemId).then(response => {
                this.orderItemObj = response.data.data
                let orderRefunds = this.orderItemObj.listOrderRefunds[0]
                this.orderRefundsStatusList = []
                if (this.orderItemObj.status == '1') {//处理退款申请
                    if (orderRefunds.status == '1' && !this.roles.includes('3')) {
                        this.orderRefundsStatusList = [{
                            label: '同意退款',
                            value: '11'
                        }, {
                            label: '拒绝退款',
                            value: '12'
                        }]
                    } else {
                        this.orderRefundsStatusList = [{
                            label: '同意退款',
                            value: '11'
                        }]
                    }
                }
                if (this.orderItemObj.status == '2') {
                    //处理退货退款申请
                    // if (orderRefunds.status == '2') {
                    //     this.orderRefundsStatusList = [{
                    //         label: '等待退货',
                    //         value: '21'
                    //     }, {
                    //         label: '拒绝退货退款',
                    //         value: '22'
                    //     }]
                    // }
                    if (orderRefunds.status == '2' && !this.roles.includes('3')) {
                        this.orderRefundsStatusList = [{
                            label: '等待退货',
                            value: '21'
                        }, {
                            label: '拒绝退货退款',
                            value: '22'
                        }]
                    } else if (orderRefunds.status == '2' && this.roles.includes('3')) {
                        this.orderRefundsStatusList = [{
                            label: '等待退货',
                            value: '21'
                        }]
                    } else if (orderRefunds.status == '21' && !this.roles.includes('3')) {
                        this.orderRefundsStatusList = [{
                            label: '收到退货同意退款',
                            value: '211'
                        }, {
                            label: '收到退货拒绝退款',
                            value: '212'
                        }]
                    } else if (orderRefunds.status == '21' && this.roles.includes('3')) {
                        this.orderRefundsStatusList = [{
                            label: '收到退货同意退款',
                            value: '211'
                        }]
                    }
                    // if (orderRefunds.status == '21') {
                    //     this.orderRefundsStatusList = [{
                    //         label: '收到退货同意退款',
                    //         value: '211'
                    //     }, {
                    //         label: '收到退货拒绝退款',
                    //         value: '212'
                    //     }]
                    // }
                }
                if (orderRefunds.receiveInfo && orderRefunds.receiveInfo.phone) {
                    this.orderRefunds.receiveInfo = orderRefunds.receiveInfo;
                }
            })
        },
        searchChange(params, done) {
            params = this.filterForm(params)
            this.paramsSearch = params
            this.page.currentPage = 1
            this.getPage(this.page, params)
            done()
        },
        sortChange(val) {
            let prop = val.prop ? val.prop.replace(/([A-Z])/g, "_$1").toLowerCase() : '';
            if (val.order == 'ascending') {
                this.page.descs = []
                this.page.ascs = prop
            } else if (val.order == 'descending') {
                this.page.ascs = []
                this.page.descs = prop
            } else {
                this.page.ascs = []
                this.page.descs = []
            }
            this.getPage(this.page)
        },
        getPage(page, params) {
            this.tableLoading = true
            getPage(Object.assign({
                current: page.currentPage,
                size: page.pageSize,
                descs: this.page.descs,
                ascs: this.page.ascs,
            }, params, this.paramsSearch)).then(response => {
                    this.tableData = response.data.data.records
                    this.page.total = response.data.data.total
                    this.page.currentPage = page.currentPage
                    this.page.pageSize = page.pageSize
                    this.tableLoading = false
                }
            ).catch(() => {
                this.tableLoading = false
            })
        },
        /**
         * @title 数据删除
         * @param row 为当前的数据
         * @param index 为当前删除数据的行数
         *
         **/
        handleDel: function (row, index) {
            var _this = this
            this.$confirm('是否确认删除此数据', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(function () {
                return delObj(row.id)
            }).then(data => {
                    _this.$message({
                        showClose: true,
                        message: '删除成功',
                        type: 'success'
                    })
                    this.getPage(this.page)
                }
            ).catch(function (err) {
            })
        },
        /**
         * @title 数据更新
         * @param row 为当前的数据
         * @param index 为当前更新数据的行数
         * @param done 为表单关闭函数
         *
         **/
        handleUpdate: function (row, index, done, loading) {
            putObj(row).then(data => {
                this.$message({
                    showClose: true,
                    message: '修改成功',
                    type: 'success'
                })
                done()
                this.getPage(this.page)
            }).catch(() => {
                loading()
            })
        },
        /**
         * @title 数据添加
         * @param row 为当前的数据
         * @param done 为表单关闭函数
         *
         **/
        handleSave: function (row, done, loading) {
            addObj(row).then(data => {
                this.$message({
                    showClose: true,
                    message: '添加成功',
                    type: 'success'
                })
                done()
                this.getPage(this.page)
            }).catch(() => {
                loading()
            })
        },
        /**
         * 刷新回调
         */
        refreshChange(page) {
            this.getPage(this.page)
        },
        // 获取店铺联系方式自动填入退货收件数据中
        orderRefundsChange() {
            //请求店铺信息获取店铺联系方式
            if (!this.orderRefunds.receiveInfo || !this.orderRefunds.receiveInfo.phone) {
                getShopInfo(this.orderItemObj.shopId).then(response => {
                    let res = response.data.data || {}
                    this.orderRefunds.receiveInfo.phone = res.phone;
                    this.orderRefunds.receiveInfo.nickName = res.name;
                    this.orderRefunds.receiveInfo.address = res.address;
                });
            }

        },
        // 查询物流信息
        handleLogisticsInfo(row, phone) {
            let num = row.logisticsNo;
            let com = row.logisticsCode;
            if (!num) {
                this.$message.error("物流单号不能为空");
                return;
            }
            // let num = "YT7467491971543";
            // let com = "yuantong";
            let logisticsInfo = getStore(com + num);
            this.logisticsInfo.params = {
                phone: phone,
                com: com,
                num: num
            };
            if (logisticsInfo) {
                // 如果缓存时间超过30分钟就刷新数据
                if (new Date().getTime() - logisticsInfo.time > 30 * 60 * 1000) {
                    this.refreshLogisticsInfoData();
                } else {
                    // 否则使用缓存数据
                    this.logisticsInfo.data = logisticsInfo.data;
                    this.logisticsInfo.updateTime = this.$moment(logisticsInfo.time).format(
                        "YYYY-MM-DD HH:mm:ss"
                    );
                    this.logisticsInfo.dialog = true;
                }
            } else {
                this.refreshLogisticsInfoData();
            }
        },
        // 刷新请求快递100的物流信息
        refreshLogisticsInfoData() {
            getLogisticsInfo(this.logisticsInfo.params).then(response => {
                if (response.data && response.data.data && response.data.data.body) {
                    try {
                        this.logisticsInfo.data = JSON.parse(response.data.data.body);
                    } catch (e) {
                        this.logisticsInfo.data = {};
                    }
                }
                if (this.logisticsInfo.data.message == "ok") {
                    // 缓存物流信息30分钟,避免重复请求刷新(ps: 用户可以手动实时刷新)
                    setStore({
                            name: this.logisticsInfo.params.com + this.logisticsInfo.params.num,
                            content: {
                                data: this.logisticsInfo.data,
                                time: new Date().getTime()
                            }
                        }
                    );
                    this.logisticsInfo.updateTime = this.$moment(new Date().getTime()).format(
                        "YYYY-MM-DD HH:mm:ss"
                    );
                    this.logisticsInfo.dialog = true;
                } else {
                    this.$message({
                        showClose: true,
                        message: this.logisticsInfo.data.message,
                        type: "error"
                    });
                }
            });
        },
        // 根据code找到logisticsDic 的name
        getLogisticsName(code) {
            let name = "暂未填写数据";
            if (code) {
                this.logisticsDic.forEach(item => {
                    if (item.code == code) {
                        name = item.name;
                    }
                });
            }
            return name;
        }
    }
}
</script>

<style lang="scss" scoped>
.time {
    font-size: 13px;
    color: #999;
}

.bottom {
    margin-top: 13px;
    line-height: 12px;
}

.button {
    padding: 0;
    float: right;
}

.image {
    width: 100%;
    display: block;
}

.clearfix:before,
.clearfix:after {
    display: table;
    content: "";
}

.clearfix:after {
    clear: both
}
</style>
