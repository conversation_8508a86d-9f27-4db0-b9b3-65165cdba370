/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import request from '@/router/axios'

export function getPage(query) {
  return request({
    url: '/mall/logistics/page',
    method: 'get',
    params: query
  })
}

export function addObj(obj) {
  return request({
    url: '/mall/logistics',
    method: 'post',
    data: obj
  })
}

export function delObj(code) {
  return request({
    url: '/mall/logistics/' + code,
    method: 'delete'
  })
}

export function putObj(obj) {
  return request({
    url: '/mall/logistics',
    method: 'put',
    data: obj
  })
}

/**
 * 根据快递公司编码实施查询物流信息
 * 参数说明: https://api.kuaidi100.com/document/5f0ffb5ebc8da837cbd8aefc
 * phone 手机号(可选)
 * com 快递公司编码
 * num 快递单号
 * @param query
 * @returns
 */
export function getLogisticsInfo(query) {
  return request({
    url: '/mall/logistics',
    method: 'get',
    params: query
  })
}
