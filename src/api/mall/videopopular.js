import request from '@/router/axios'

export function getPage(query) {
    return request({
        url: '/mall/videopopular/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/videopopular',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/videopopular/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/videopopular/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/videopopular',
        method: 'put',
        data: obj
    })
}
export function changeObj(obj) {
    return request({
        url: '/mall/videopopular/releaseVideo',
        method: 'post',
        data: obj
    })
}
export function putObjVerify(obj) {
    return request({
        url: '/mall/videopopular/batchReleaseVideo',
        method: 'post',
        data: obj
    })
}

