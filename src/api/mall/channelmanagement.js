/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import request from '@/router/axios'
import {getStore} from "@/util/store";

export function getPage(query) {
    return request({
        url: '/mall/channelmanagement/page',
        method: 'get',
        params: query
    })
}

export function addObj(obj) {
    return request({
        url: '/mall/channelmanagement',
        method: 'post',
        data: obj
    })
}

export function getObj(id) {
    return request({
        url: '/mall/channelmanagement/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return request({
        url: '/mall/channelmanagement/' + id,
        method: 'delete'
    })
}

export function putObj(obj) {
    return request({
        url: '/mall/channelmanagement',
        method: 'put',
        data: obj
    })
}

// 生成微信二维码
export function generateWXCode(data) {
    return request({
        url: '/weixin//wxqrcode/unlimitedToImg',
        method: 'post',
        data
    })
}

// 生成微信二维码2
export function generateWXCode2(data) {
    return request({
        url: '/mall/channelmanagement/createQrCode',
        method: 'post',
        data
    })
}

// 店铺列表
export function getStoreList(params) {
    return request({
        url: '/mall/shopinfo/list',
        method: 'get',
        params,
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        }
    })
}

// 商品列表
export function getProductList(params) {
    return request({
        url: '/mall/goodsspu/page',
        method: 'get',
        params,
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        }
    })
}

// 服务信息查询
export function serviceInfoInquiry(params) {
    return request({
        url: `/mall/channelmanagement/getChannelPage`,
        method: 'get',
        params,
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        }
    })
}

// 渠道商品列表保存
export function channelGoods(data) {
    return request({
        url: `/mall/channelmanagement/channelGoods`,
        method: 'post',
        data,
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        }
    })
}

// 渠道商品关联表删除
export function channelGoodsDel(data) {
    return request({
        url: `/mall/channelgoods/${data.id}`,
        method: 'DELETE',
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        }
    })
}

// 单商品批量保存
export function soleGoodsSave(data) {
    return request({
        url: `/mall/channelmanagement/soleGoodsSave`,
        method: 'post',
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        },
        data
    })
}

// 单店铺批量保存
export function soleShopSave(data) {
    return request({
        url: `/mall/channelmanagement/soleShopSave`,
        method: 'post',
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        },
        data
    })
}

// 单店铺批量保存
export function qrcodeManagementDel(data) {
    return request({
        url: `/mall/qrcodemanagement/${data.id}`,
        method: 'DELETE',
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        }
    })
}

// 基罩务明细查询
export function getChannelOrder(params) {
    return request({
        url: `/mall/channelmanagement/getChannelOrder`,
        method: 'get',
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        },
        params
    })
}

// 服务明细统计
export function serviceStatistics(params) {
    return request({
        url: `/mall/channelmanagement/serviceStatistics/${params.id}`,
        method: 'get',
        headers: {
            "switch-tenant-id": getStore({name: 'switchTenantId'}) || ""
        },
        params
    })
}
