import AMapLoader from '@amap/amap-jsapi-loader';

export default function initMapLoader() {
    return new Promise((resolve, reject) => {
        AMapLoader.reset()
        window._AMapSecurityConfig = {
            securityJsCode: "7d3c4b2ec99647f68ae64eb17277a111",
        };
        AMapLoader.load({
            key: "54050de07a172107e30c01c3b7c48580", // 申请好的Web端开发者Key，首次调用 load 时必填
            version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
            plugins: ["AMap.Scale", "AMap.Circle", "AMap.CircleEditor", "AMap.Polygon", "AMap.PolygonEditor"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
        }).then((AMap) => {
            resolve(AMap)
        }).catch((e) => {
            reject(e);
        });
    })
}
