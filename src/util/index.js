/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @param {Array} arr1
 * @param {Array} arr2
 * @returns {Array}
 */
export function diffArary(arr1, arr2) {
  arr1 = uniqueArr(arr1)
  arr2 = uniqueArr(arr2)
  return arr1.concat(arr2).filter(arg => !(arr1.includes(arg)  && arr2.includes(arg)))
}

/**
 * 将数组转换为字符串
 * @param {Array} arr
 * @returns {string}
 */
export function convertArrayToString(arr) {
    return arr.map(coord => coord[0] + "," + coord[1]).join(";");
}

/**
 * 将字符串转换为数组
 * @param {string} str
 * @returns {Array}
 */
export function convertStringToArray(str) {
    return str.split(";")
              .map(coordPair => coordPair.split(",")
                                         .map(Number));
  }


  export function formatTime(timeInfo, inputTime) {
    console.log(timeInfo, 'timeInfo');
    console.log(inputTime, 'inputTime');

    const { day, hour1, minute1, hour2, minute2 } = timeInfo;
    const inputDate = new Date(inputTime);
    const inputDay = inputDate.getDate();
    const inputMonth = inputDate.getMonth();
    const inputYear = inputDate.getFullYear();

    const time1Formatted = `${hour1.padStart(2, '0')}:${minute1.padStart(2, '0')}`;
    const time2Formatted = `${hour2.padStart(2, '0')}:${minute2.padStart(2, '0')}`;

    const today = new Date();
    const todayDay = today.getDate();
    const todayMonth = today.getMonth();
    const todayYear = today.getFullYear();

    let targetDate = new Date(inputYear, inputMonth, inputDay + parseInt(day) - 1);
    const targetMonth = (targetDate.getMonth() + 1).toString().padStart(2, '0');
    const targetDay = targetDate.getDate().toString().padStart(2, '0');
    const targetYear = targetDate.getFullYear(); // 补上 targetYear

    if (day === "1") {
        // 如果是今天，显示“今天”
        if (targetYear === todayYear && targetMonth === (todayMonth + 1).toString().padStart(2, '0') && targetDay === todayDay.toString().padStart(2, '0')) {
            return `今天 ${time1Formatted}-${time2Formatted}`;
        } else {
            // 否则显示日期
            return `${targetYear}-${targetMonth}-${targetDay}-${time1Formatted}-${time2Formatted}`; // 使用 targetYear
        }
    } else if (day === "2") {
        return `明天 ${time1Formatted}-${time2Formatted}`; // 这里可以根据需要改成显示日期
    } else {
        return `${targetYear}-${targetMonth}-${targetDay}-${time1Formatted}-${time2Formatted}`; // 使用 targetYear
    }
}

