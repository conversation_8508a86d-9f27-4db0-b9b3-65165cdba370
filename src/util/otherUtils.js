// 深拷贝
export function deepCopy(data) {
  return JSON.parse(JSON.stringify(data));
}

// 判断对象是否为空
export function isEmptyObj(obj) {
  return obj && Object.keys(obj).length > 0;
}

// 判断数组是否为空
export function isEmptyArr(arr) {
  return arr && Array.isArray(arr) && arr.length > 0;
}


export function debounce(fn, delay=800) {
    let time = null
    return function() {
        let context = this;//记录一下this指向
        let args = arguments;
        //清除定时任务
        if (time) clearTimeout(time);
        time = setTimeout(function() {
            time = null;
            fn.apply(context, args)
        }, delay)
    }
}


export function throttle(fn, delay) {
    // 时间戳
    let timeTwo = 0 //new Date();
    // 定时器
    let timeThree = null;
    return function() {
        let context = this;
        let args = arguments;
        let now = new Date()

        // !!!!时间戳实现【new Date()虽然获取结果不是时间戳但是计算结果会自动转化为时间戳】
        // if(now-timeTwo>=delay){
        //     fn.apply(context,args);
        //     timeTwo=new Date();
        // }

        // !!!!定时器实现
        // if (!timeThree) {
        //     timeThree = setTimeout(function () {
        //         fn.apply(context, args);
        //         timeThree=null;
        //     }, delay)
        // }

        // 结合 ps:最后一次触发在固定频率内会在延迟后触发
        let wait = delay - (now - timeTwo)
        clearTimeout(timeThree)
        if (wait <= 0) {
            fn.apply(context, args);
            timeTwo = new Date();
        } else {
            timeThree = setTimeout(function() {
                fn.apply(context, args);
            }, delay)
        }
    }
}
