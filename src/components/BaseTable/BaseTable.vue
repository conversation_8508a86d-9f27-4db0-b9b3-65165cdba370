<template>
    <div class="BaseTable w100 df flc jc-fs alc">
        <el-table
            max-height="400"
            class="w100"
            :data="tableCon.data?tableCon.data:[]"
            :row-key="tableCon.rowKey?tableCon.rowKey:'id'"
            :tree-props="tableCon.treeProps?tableCon.treeProps:{}"
            @selection-change="handleSelectionChange"
            @current-change="handleCurrentChange"
            :header-cell-style="{backgroundColor: '#F5F7FA',color:'#909399'}"
            :highlight-current-row="true"
            border>
            <template v-for="(item,index) in tableCon.columns?tableCon.columns:[]">
                <!--多选框-->
                <el-table-column :key="index" type="selection" width="55" :align="item.align?item.align:'center'"
                                 v-if="item.slotName === 'selection'"></el-table-column>
                <!--第一列序号-->
                <el-table-column :key="index" type="index" :width="item.width?item.width:'80'" :label="item.label" :align="item.align"
                                 v-else-if="item.slotName === 'serialNum'"></el-table-column>
                <!--自定义文字1-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'customText'">
                    <template slot-scope="scope">
                        <el-tag v-if="item['type'] === 'tag'">{{ item['textArr'][Number(scope.row[item.prop])] }}</el-tag>
                        <div class="text-box dfc" v-else>
                            <p>{{ item['textArr'][Number(scope.row[item.prop])] }}</p>
                        </div>
                    </template>
                </el-table-column>
                <!--自定义文字2-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'customText2'">
                    <template slot-scope="scope">
                        <div class="text-box dfc">
                            <p>{{ scope.row[item.prop] }} ~ {{ scope.row[item.subProp] }}</p>
                        </div>
                    </template>
                </el-table-column>
                <!--自定义文字3-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'customText3'">
                    <template slot-scope="scope">
                        <div class="text-box dfc">
                            <p>{{ Number(scope.row[item.prop]) * scope.row[item.subProp] }}<span v-show="item.unit">{{ item.unit }}</span></p>
                        </div>
                    </template>
                </el-table-column>
                <!--图片展示1-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'picShow1'">
                    <template slot-scope="scope">
                        <el-image
                            style="width: 50px; height: 50px; cursor: pointer;"
                            :preview-src-list="scope.row[item.prop]"
                            :src="scope.row[item.prop][0]"></el-image>
                    </template>
                </el-table-column>
                <!--图片展示2-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'picShow2'">
                    <template slot-scope="scope">
                        <el-image
                            style="width: 50px; height: 50px; cursor: pointer;"
                            :preview-src-list="[scope.row[item.prop]]"
                            :src="scope.row[item.prop]"></el-image>
                    </template>
                </el-table-column>
                <!--二维码展示1-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'code1'">
                    <template slot-scope="scope">
                        <el-image
                            style="width: 50px; height: 50px; cursor: pointer;"
                            :src="scope.row[item.prop]"></el-image>
                    </template>
                </el-table-column>
                <!--二维码展示2-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'code2'">
                    <template slot-scope="scope">
                        <el-image
                            style="width: 50px; height: 50px; cursor: pointer;"
                            :preview-src-list="isEmptyArr(scope.row[item.prop])?scope.row[item.prop]:[scope.row[item.prop]]"
                            :src="scope.row[item.prop]" v-if="scope.row[item.prop]"></el-image>
                        <el-link :underline="false" type="primary" @click="clickCon(item.prop, scope.row)" v-else>生成二维码</el-link>
                    </template>
                </el-table-column>

                <!--开关switch-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 v-else-if="item.slotName === 'switch'">
                    <template slot-scope="scope">
                        <el-switch
                            v-model="scope.row[item.prop]"
                            @change="clickCon(item.prop, scope.row)"
                            :active-text="item.activeText?item.activeText:''"
                            :inactive-text="item.inactiveText?item.inactiveText:''">
                        </el-switch>
                    </template>
                </el-table-column>

                <!--操作-->
                <el-table-column :key="index" :width="item.width?item.width:''"
                                 :label="item.label" :align="item.align"
                                 v-else-if="item.slotName === 'operation'">
                    <template slot-scope="scope">
                        <template v-for="(ite,ind) in item.btnList">
                            <el-link :underline="false" :style="{color:ite.color?ite.color:''}"
                                     :class="item.btnList.length >= 1 ? 'm-r-12':''"
                                     @click="clickCon(ite.type, scope.row)" :key="ind">
                                {{ ite.text }}
                            </el-link>
                        </template>
                        <el-popconfirm title="确定要删除当前数据？"
                                       v-if="item.isDel" @confirm="clickCon('del', scope.row)">
                            <el-link :underline="false" slot="reference" style="color: rgba(251, 84, 81, 1);">删除
                            </el-link>
                        </el-popconfirm>

                    </template>
                </el-table-column>
                <!--普通样式-->
                <el-table-column :key="index" :label="item.label" :align="item.align"
                                 :width="item.width?item.width:''"
                                 :prop="item.prop"
                                 :sortable="item.sortable?item.sortable:false"
                                 :show-overflow-tooltip="item.showOverflowTooltip?item.showOverflowTooltip:''"
                                 v-else></el-table-column>
            </template>
        </el-table>
        <div class="table-bot w100 df flr jc-fe alc"
             v-if="tableCon.paginationVisible ?tableCon.paginationVisible : ''">
            <el-pagination background @size-change="(val)=>clickCon('pageSize',val)"
                           @current-change="(val)=>clickCon('pageNo',val)"
                           :current-page="tableCon.page?tableCon.page:1"
                           :page-sizes="[10, 20, 30, 40]" :page-size="tableCon.pageSize?tableCon.pageSize:10"
                           layout="total, sizes, prev, pager, next, jumper"
                           :total="tableCon.total || tableCon.total === 0 ? tableCon.total:10">
            </el-pagination>
        </div>
    </div>
</template>

<script name="BaseTable">
import {isEmptyArr} from "@/util/otherUtils";

export default {
    props: {
        tableData: {
            type: Object,
            default: ()=>{
                return {}
            }
        }
    },
    data() {
        return {
            tableCon: {
                total: 10,
                page: 1,
                pageSize: 10,
                paginationVisible: true,
                treeProps:{children: 'children'},
                data: [
                    {
                        channelName: "渠道名称1",
                        channelSource: "渠道来源1"
                    },
                    {
                        channelName: "渠道名称2",
                        channelSource: "渠道来源2"
                    }
                ],
                columns: [
                    {
                        label: "序号",
                        slotName: "serialNum",
                        align: "center"
                    },
                    {
                        label: "渠道名称",
                        prop: "channelName",
                        align: "center"
                    },
                    {
                        label: "渠道来源",
                        prop: "channelSource",
                        align: "center"
                    },
                    {
                        label: "操作",
                        slotName: "operation",
                        isDel: true,
                        btnList: [
                            {
                                text: '新增',
                                color: 'rgba(0, 160, 233, 1)'
                            },
                            {
                                text: '编辑',
                                color: 'rgba(0, 160, 233, 1)'
                            }
                        ]
                    }
                ]
            }
        }
    },
    watch: {
        tableData: {
            immediate: true,
            handler(newVal) {
                if(newVal) {
                    this.tableCon = Object.assign(newVal)
                }
            }
        }
    },
    mounted() {
    },
    methods: {
        isEmptyArr,
        clickCon(type, con) {
            let obj
            let tablePage = {
                page: this.tableCon.page,
                pageSize: this.tableCon.pageSize
            }
            if (type === 'pageSize' || type === 'pageNo') {
                if (type === 'pageSize') {
                    tablePage.pageSize = con
                } else if (type === 'pageNo') {
                    tablePage.page = con
                }
                obj = {
                    typeStr: 'page',
                    data: tablePage
                }
            } else {
                obj = {
                    typeStr: type,
                    data: con
                }
            }
            this.$emit('tableClick', obj)
        },

        // 多选
        handleSelectionChange(e) {
            let backCon = {
                typeStr: 'selectionChange',
                data: e
            }
            this.$emit('tableClick', backCon)
        },

        // 选择自身
        handleCurrentChange(e) {
            // console.log('选择自身', e)
        }
    }
}
</script>

<style lang="scss" scoped>
.BaseTable {
    .table-bot {
        padding: 20px 0 0 0;
    }
}
</style>
