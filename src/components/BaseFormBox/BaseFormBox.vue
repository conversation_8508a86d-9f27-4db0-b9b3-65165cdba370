<template>
    <div class="BaseFormBox">
        <el-dialog :title="dialogObj.title[dialogObj.type]"
                   :visible.sync="dialogObj.show"
                   append-to-body
                   :width="dialogObj.width?dialogObj.width:'60%'"
                   @close="formBackCon('close')">
            <el-form
                :model="dialogObj.formData"
                :rules="dialogObj.formRules"
                ref="ruleForm"
                class="formDialog-con-ruleForm df flrw jc-sb alc"
                label-width="120px">
                <template v-for="(item, index) in dialogObj.formList">
                    <el-form-item
                        :label="item.label"
                        :key="index"
                        :prop="item.prop"
                        :style="[ { width: item['allWidth']?'100%':'49%' } ]"
                        class="fs0"
                        v-if="item.visible !== false">
                        <div class="item-box w100 dfc" v-if="item['type'] === 'input'">
                            <el-input
                                v-model="dialogObj.formData[item.prop]"
                                :placeholder="item.pla"
                                :disabled="item.disabled ? item.disabled : false"
                            >
                                <template slot="append" v-if="item.unit">{{ item.unit }}</template>
                            </el-input>
                        </div>
                        <div class="item-box w100 dfc" v-if="item['type'] === 'textarea'">
                            <el-input
                                type="textarea"
                                v-model="dialogObj.formData[item.prop]"
                                :placeholder="item.pla"
                                :rows="item['row']?item['row']:2"
                                :disabled="item.disabled ? item.disabled : false"
                            >
                                <template slot="append" v-if="item.unit">{{ item.unit }}</template>
                            </el-input>
                        </div>
                        <div class="item-box w100 dfc" v-else-if="item['type'] === 'select'">
                            <el-select
                                :placeholder="item.pla"
                                v-model="dialogObj.formData[item.prop]"
                                style="width: 100%"
                                :clearable="item.clearable?item.clearable:false"
                                :disabled="item.disabled ? item.disabled : false" @change="selectChange($event,item)">
                                <el-option
                                    v-for="(ite, ind) in item.options"
                                    :key="ind"
                                    :label="item.fieldNames? ite[item.fieldNames.label] : ite.label"
                                    :value="item.fieldNames? ite[item.fieldNames.value] : ite.value">
                                </el-option>
                            </el-select>
                        </div>
                        <div class="item-box w100 dfc" style="height: 40px;" v-else-if="item['type'] === 'radio'">
                            <el-radio-group class="w100" v-model="dialogObj.formData[item.prop]" @change="radioChange">
                                <template v-for="(ite,ind) in item.options">
                                    <el-radio :label="ite.value" :key="ind">{{ ite.label }}</el-radio>
                                </template>
                            </el-radio-group>
                        </div>
                        <div class="item-box w100 df flr jc-fs alc" v-else-if="item['type'] === 'customize1'">
                            <el-input v-model="dialogObj.formData[item.prop][0]"></el-input>
                            <p class="fs0" style="margin: 0 10px;">公里内</p>
                            <el-input v-model="dialogObj.formData[item.prop][1]"></el-input>
                            <p class="fs0" style="margin: 0 10px;">元；</p>
                            <p class="fs0" style="margin: 0 10px;">每超出</p>
                            <el-input v-model="dialogObj.formData[item.prop][2]"></el-input>
                            <p class="fs0" style="margin: 0 10px;">公里，加</p>
                            <el-input v-model="dialogObj.formData[item.prop][3]"></el-input>
                            <p class="fs0" style="margin-left: 10px;">元</p>
                        </div>
                        <div class="item-box w100" v-else-if="item['type'] === 'customize3'">
                            <OptionalTime @timeChange="timeChange($event, item)"></OptionalTime>
                        </div>
                        <div class="item-box w100 dfc" v-else-if="item['type'] === 'customScope1'">
                            <DeliveryRange></DeliveryRange>
                        </div>
                        <div class="item-box w100 dfc" v-if="item['type'] === 'uploadQRCode'">
                            <div class="box-text w100 df flr jc-fs alc" v-if="!dialogObj.formData[item.prop]">
                                <el-link type="primary" :underline="false" @click="generateQRCode(item.prop)">生成二维码</el-link>
                            </div>
                            <div class="box-img w100 df flr jc-fs alc" v-else>
                                <el-image :src="dialogObj.formData[item.prop]" style="width: 100px; height: 100px"></el-image>
                            </div>
                        </div>
                    </el-form-item>
                </template>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="formBackCon('close')">取 消</el-button>
                <el-button type="primary" @click="formBackCon('submit')">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script name="BaseFormBox">
import OptionalTime from "@/components/BaseFormBox/OptionalTime/OptionalTime.vue";
import DeliveryRange from "@/components/BaseFormBox/DeliveryRange/DeliveryRange.vue";
export default {
    components: {
        OptionalTime,
        DeliveryRange
    },
    props: {
        formConfig: {
            type: Object,
            default: ()=>{
                return {}
            }
        }
    },
    data() {
        return {
            dialogObj: {
                title: {
                    add: "新增",
                    edit: "编辑"
                },
                type: "add",
                show: false,
                formList: [],
                formData: {},
                formRules: {}
            }
        }
    },
    watch: {
        formConfig: {
            handler(newVal) {
                if(newVal) {
                    this.dialogObj = Object.assign(this.dialogObj, newVal)
                }
            },
            deep: true,
            immediate: true
        }
    },
    mounted() {
    },
    methods: {
        // 时间返回
        timeChange(data, data2) {
            this.$emit('timeChange', { data, type: data2.prop })
        },
        // 单选返回
        radioChange() {
            this.$emit('radioChange', this.dialogObj.formData)
        },
        // 表单返回
        formBackCon(type) {
            let backCon = {
                type,
                data: this.dialogObj.formData
            }
            this.$emit('formBack', backCon)
        },

        // 生成二维码
        async generateQRCode(type) {
            this.$emit('formBack', type)
        },

        // 选择
        selectChange(val, data) {
            let obj = {
                rules: data.rules,
                val: val
            }
            if (data.isChange) {
                this.$emit('selectChange', obj)
            }

        }
    }
}
</script>

<style lang="scss" scoped>
</style>
