<template>
    <div class="OptionalTime">
        <div class="optional-list w100 df flc jc-fs alc">
            <template v-for="(item,index) in timeList">
                <div class="optional-item w100 df flr jc-fs alc" :key="index">
                    <el-date-picker
                        class="date-picker-con"
                        v-model="item.value"
                        type="daterange"
                        @change="changesTime"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                    <el-button type="primary" icon="el-icon-plus" circle v-if="index === 0" @click="addTimeCon"></el-button>
                    <el-button type="danger" icon="el-icon-delete" circle v-else @click="delTimeCon(index)"></el-button>
                </div>
            </template>
        </div>
    </div>
</template>

<script name="OptionalTime">
import {deepCopy} from "@/util/otherUtils";

export default {
  data() {
    return {
        timeList: [
            {
                value: ""
            }
        ]
    }
  },
  mounted() {
  },
  methods: {
      // 选择时间
      changesTime() {
          this.$emit("timeChange", this.timeList);
      },
      // 删除时间内容
      delTimeCon(index) {
          let list = deepCopy(this.timeList);
          list.splice(index, 1);
          this.timeList = list;
      },
      // 添加时间内容
      addTimeCon() {
          this.timeList.push({
              value: ""
          })
      }
  }
}
</script>

<style lang="scss" scoped>
.OptionalTime {
    .optional-list {
        .optional-item {
            margin-bottom: 20px;
            .date-picker-con {
                margin-right: 20px;
            }
        }
        .optional-item:last-child {
            margin-bottom: 0;
        }
    }
}
</style>
