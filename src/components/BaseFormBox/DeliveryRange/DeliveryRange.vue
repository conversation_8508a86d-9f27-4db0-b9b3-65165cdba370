<template>
    <div class="DeliveryRange w100 df flc jc-fs als">
        <el-radio-group class="w100" v-model="radioData.value">
            <template v-for="(item,index) in radioData.list">
                <el-radio :label="item.value" :key="index">{{ item.label }}</el-radio>
            </template>
        </el-radio-group>
        <el-cascader v-model="cascaderData.value" :options="cascaderData.options"></el-cascader>
        <BaseMap @load="mapLoad"></BaseMap>
    </div>
</template>

<script name="DeliveryRange w100 df flc jc-fs als">
import BaseMap from "@/components/BaseMap/BaseMap.vue";
import {deepCopy} from "@/util/otherUtils";
export default {
    components: {
        BaseMap
    },
    data() {
        return {
            mapInfo: {},
            polygon: "", // 多边形
            polyEditor: "",// 多边形编辑器
            circle: "", // 圆形
            circleEditor: "", // 圆形编辑器
            cascaderData: {
                value: "",
                options: [
                    {
                        value: 'zhinan',
                        label: '指南',
                        children: [{
                            value: 'shejiyuan<PERSON>',
                            label: '设计原则',
                            children: [{
                                value: 'yizhi',
                                label: '一致'
                            }, {
                                value: 'fankui',
                                label: '反馈'
                            }, {
                                value: 'xiaolv',
                                label: '效率'
                            }, {
                                value: 'kekong',
                                label: '可控'
                            }]
                        },
                            {
                                value: 'daohang',
                                label: '导航',
                                children: [{
                                    value: 'cexiangdaohang',
                                    label: '侧向导航'
                                }, {
                                    value: 'dingbudaohang',
                                    label: '顶部导航'
                                }]
                            }]
                    },
                    {
                        value: 'zujian',
                        label: '组件',
                        children: [{
                            value: 'basic',
                            label: 'Basic',
                            children: [{
                                value: 'layout',
                                label: 'Layout 布局'
                            }, {
                                value: 'color',
                                label: 'Color 色彩'
                            }, {
                                value: 'typography',
                                label: 'Typography 字体'
                            }, {
                                value: 'icon',
                                label: 'Icon 图标'
                            }, {
                                value: 'button',
                                label: 'Button 按钮'
                            }]
                        },
                            {
                                value: 'form',
                                label: 'Form',
                                children: [{
                                    value: 'radio',
                                    label: 'Radio 单选框'
                                }, {
                                    value: 'checkbox',
                                    label: 'Checkbox 多选框'
                                }, {
                                    value: 'input',
                                    label: 'Input 输入框'
                                }, {
                                    value: 'input-number',
                                    label: 'InputNumber 计数器'
                                }, {
                                    value: 'select',
                                    label: 'Select 选择器'
                                }, {
                                    value: 'cascader',
                                    label: 'Cascader 级联选择器'
                                }, {
                                    value: 'switch',
                                    label: 'Switch 开关'
                                }, {
                                    value: 'slider',
                                    label: 'Slider 滑块'
                                }, {
                                    value: 'time-picker',
                                    label: 'TimePicker 时间选择器'
                                }, {
                                    value: 'date-picker',
                                    label: 'DatePicker 日期选择器'
                                }, {
                                    value: 'datetime-picker',
                                    label: 'DateTimePicker 日期时间选择器'
                                }, {
                                    value: 'upload',
                                    label: 'Upload 上传'
                                }, {
                                    value: 'rate',
                                    label: 'Rate 评分'
                                }, {
                                    value: 'form',
                                    label: 'Form 表单'
                                }]
                            },
                            {
                                value: 'data',
                                label: 'Data',
                                children: [{
                                    value: 'table',
                                    label: 'Table 表格'
                                }, {
                                    value: 'tag',
                                    label: 'Tag 标签'
                                }, {
                                    value: 'progress',
                                    label: 'Progress 进度条'
                                }, {
                                    value: 'tree',
                                    label: 'Tree 树形控件'
                                }, {
                                    value: 'pagination',
                                    label: 'Pagination 分页'
                                }, {
                                    value: 'badge',
                                    label: 'Badge 标记'
                                }]
                            },
                            {
                                value: 'notice',
                                label: 'Notice',
                                children: [{
                                    value: 'alert',
                                    label: 'Alert 警告'
                                }, {
                                    value: 'loading',
                                    label: 'Loading 加载'
                                }, {
                                    value: 'message',
                                    label: 'Message 消息提示'
                                }, {
                                    value: 'message-box',
                                    label: 'MessageBox 弹框'
                                }, {
                                    value: 'notification',
                                    label: 'Notification 通知'
                                }]
                            },
                            {
                                value: 'navigation',
                                label: 'Navigation',
                                children: [{
                                    value: 'menu',
                                    label: 'NavMenu 导航菜单'
                                }, {
                                    value: 'tabs',
                                    label: 'Tabs 标签页'
                                }, {
                                    value: 'breadcrumb',
                                    label: 'Breadcrumb 面包屑'
                                }, {
                                    value: 'dropdown',
                                    label: 'Dropdown 下拉菜单'
                                }, {
                                    value: 'steps',
                                    label: 'Steps 步骤条'
                                }]
                            },
                            {
                                value: 'others',
                                label: 'Others',
                                children: [{
                                    value: 'dialog',
                                    label: 'Dialog 对话框'
                                }, {
                                    value: 'tooltip',
                                    label: 'Tooltip 文字提示'
                                }, {
                                    value: 'popover',
                                    label: 'Popover 弹出框'
                                }, {
                                    value: 'card',
                                    label: 'Card 卡片'
                                }, {
                                    value: 'carousel',
                                    label: 'Carousel 走马灯'
                                }, {
                                    value: 'collapse',
                                    label: 'Collapse 折叠面板'
                                }]
                            }]
                    },
                    {
                        value: 'ziyuan',
                        label: '资源',
                        children: [{
                            value: 'axure',
                            label: 'Axure Components'
                        }, {
                            value: 'sketch',
                            label: 'Sketch Templates'
                        }, {
                            value: 'jiaohu',
                            label: '组件交互文档'
                        }]
                    }]
            },
            radioData: {
                value: 1,
                list: [
                    {
                        label: "圆形范围",
                        value: 1
                    },
                    {
                        label: "多边形范围",
                        value: 2
                    }
                ]
            }
        }
    },
    mounted() {
    },
    methods: {
        // 删除多边形或者圆形
        delPolygon() {
            let that = this
            let mapInfo = that.mapInfo
            // 删除多边形
            that.polyEditor.close();
            mapInfo.mapData.remove(that.polygon)
            that.polygon = null
            that.polyEditor = null
        },
        // 创建多边形
        createPolygon() {
            let that = this
            let mapInfo = that.mapInfo
            that.polyEditor = new mapInfo.AMap.PolygonEditor(mapInfo.mapData)
            that.polygon = new mapInfo.AMap.Polygon({
                path: [[116.475334, 39.997534], [116.476627, 39.998315], [116.478603, 39.99879], [116.478529, 40.000296], [116.475082, 40.000151], [116.473421, 39.998717]],
                strokeColor: "#FF33FF",
                strokeWeight: 6,
                strokeOpacity: 0.2,
                fillOpacity: 0.4,
                fillColor: '#1791fc',
                zIndex: 50,
                draggable: true, // 使多边形可拖拽
            })
            mapInfo.mapData.add([that.polygon])
            mapInfo.mapData.setFitView([that.polygon])
            that.polyEditor.addAdsorbPolygons([that.polygon])
            that.polyEditor.setTarget(that.polygon)
            that.polyEditor.open()
            // 拖拽
            that.polygon.on('dragend',(e)=>{
                console.log('多边形被拖拽:', that.polygon.getPath())
            })
            // 调整
            that.polyEditor.on('adjust',(e)=>{
                console.log('多边形被调整:', e.target.getPath())
            })
        },
        // 创建圆形
        createCircle() {
            let that = this
            let mapInfo = that.mapInfo
            console.log('我的信息：', mapInfo)
            that.circle = new mapInfo.AMap.Circle({
                center: [116.433322, 39.900255],
                radius: 1000, //半径
                borderWeight: 3,
                strokeColor: "#FF33FF",
                strokeWeight: 6,
                strokeOpacity: 0.2,
                fillOpacity: 0.4,
                strokeStyle: 'dashed',
                strokeDasharray: [10, 10],
                // 线样式还支持 'dashed'
                fillColor: '#1791fc',
                zIndex: 50,
            })
            mapInfo.mapData.add(that.circle);
            // 缩放地图到合适的视野级别
            mapInfo.mapData.setFitView([ that.circle ])
            that.circleEditor = new mapInfo.AMap.CircleEditor(mapInfo.mapData, that.circle)
            that.circleEditor.open()
            that.circleEditor.on('move', function(event) {
                console.log('触发事件：move', event)
            })

            that.circleEditor.on('adjust', function(event) {
                console.log('触发事件：adjust', event)
            })
        },
        // 地图数据
        mapLoad(mapInfo) {
            this.mapInfo = mapInfo
            this.createCircle()
        }
    }
}
</script>

<style lang="scss" scoped>
.DeliveryRange {

}
</style>
