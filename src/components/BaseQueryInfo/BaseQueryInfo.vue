<template>
    <div class="QueryInfoBox">
        <div class="info-list df flrw jc-fs alc bc1">
            <template v-for="(item,index) in dataList">
                <div class="info-item dfc" :key="index">
                    <div class="item-box item-one df flr jc-fs alc" v-if="item['type'] !== 'button'">
                        <div class="item-label dfc fs0">
                            <p>{{ item['label'] }}</p>
                        </div>
                        <div class="item-value dfc">
                            <el-input v-model="item['value']" :placeholder="item['pla']" clearable
                                      v-if="item['type'] === 'input'" @input="changeInput"></el-input>
                            <el-select v-model="item['value']" :placeholder="item['pla']" clearable
                                       v-else-if="item['type'] === 'select'" @change="queryClick('search')">
                                <el-option
                                    v-for="(ite,ind) in item.options"
                                    :key="ind"
                                    :label="item['props']?ite[item['props']['label']]:ite.label"
                                    :value="item['props']?ite[item['props']['value']]:ite.value">
                                </el-option>
                            </el-select>
                            <el-date-picker
                                @change="queryClick('search')"
                                v-else-if="item['type'] === 'datePicker'"
                                v-model="item['value']"
                                :type="item['timeType']?item['timeType']:'daterange'"
                                :format="item['format']?item['format']:'yyyy-MM-DD'"
                                :value-format="item['valueFormat']?item['valueFormat']:'yyyy-MM-DD HH:mm:SS'"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </div>
                    </div>
                    <div class="item-box dfc" v-else>
                        <el-button :type="item['btnType']" :icon="item['iconUrl']?item['iconUrl']:''" @click="queryClick(item['value'])">{{
                                item['label']
                            }}
                        </el-button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script name="QueryInfoBox">
import {debounce, deepCopy} from "@/util/otherUtils";

export default {
    props: {
        data: {
            type: Array,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return {
            dataList: []
        }
    },
    watch: {
        data: {
            handler(newVal) {
                if(newVal) {
                    this.dataList = newVal
                }
            },
            immediate: true
        }
    },
    mounted() {
    },
    methods: {
        changeInput: debounce(function() {
            this.queryClick('search')
        }),
        queryClick(type) {
            let dataListCopy = deepCopy(this.dataList)
            let backCon = {
                type,
                data: dataListCopy
            }
            this.$emit('queryClick', backCon)
        }
    }
}
</script>

<style lang="scss" scoped>
.QueryInfoBox {
    .info-list {
        padding: 20px;

        .info-item {
            .item-box {
                padding: 10px;
                margin-right: 10px;
            }

            .item-box:last-child {
                margin-right: 0;
            }

            .item-one {
                .item-label {
                    margin-right: 10px;
                }
            }
        }
    }
}
</style>
