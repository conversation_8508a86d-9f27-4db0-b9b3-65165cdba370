<template>
  <div>
    <el-button  size="small" type="text" @click="showUserDetail"
      >{{ userName || userDetail.nickName || userId }}
    </el-button>
    <el-dialog
      title="用户详情"
      :visible.sync="dialogVisibleUserDetail"
      :append-to-body="true"
      width="66%"
    >
      <div style="position: relative" v-if="dialogVisibleUserDetail">
        <avue-form :option="tableOptionUser" v-model="userDetail" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getObj as getObjUser } from "@/api/mall/userinfo";
import { tableOptionDetail as tableOptionUser } from "@/const/crud/mall/userinfo";
export default {
  props: {
    userId: {
      type: String,
      default: ""
    },
    userName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      tableOptionUser: tableOptionUser,
      dialogVisibleUserDetail: false,
      userDetail: {}
    };
  },
  watch: {
    userId: {
      handler() {
        this.getUserDetail();
      },
      immediate: true
    }
  },
  methods: {
    getUserDetail() {
      if (this.userId) {
        getObjUser(this.userId).then(res => {
          this.userDetail = res.data.data ? res.data.data : {};
        });
      }
    },
    showUserDetail() {
      if (this.userDetail.id) {
        this.dialogVisibleUserDetail = true;
        return;
      }
      if (!this.userId) {
        this.$message({
          type: 'warning',
          message: '没有查询到数据!'
        });
        return;
      }
      getObjUser(this.userId).then(res => {
        this.userDetail = res.data.data ? res.data.data : {};
        if (!this.userDetail.id) {
          this.$message({
            type: 'warning',
            message: '没有查询到数据!'
          });
        } else {
          this.dialogVisibleUserDetail = true;
        }
      });
    }
  }
};
</script>
