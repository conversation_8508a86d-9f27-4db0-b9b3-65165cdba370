<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 选择小程序页面的组件 -->
<template>
  <div>
    <div>
      <el-select  size="small" :style="{width: !isAutoWidth ? '32%' : '100%',float: !isAutoWidth ? 'left' : 'none'}" :disabled="isGoodsPage" filterable v-model="form.page" placeholder="请选择跳转页面"  @change="onChangePage">
        <el-option v-for="(item, index) in pageUrls.pages" :key="index" :label="item.name" :value="item.url">
          <div style="padding: 10px;">
            <div style="line-height: 1;">{{ item.name }}</div>
            <div style="line-height: 1.4;color: #909399; font-size: 12px">{{ item.url }}</div>
          </div>
        </el-option>
      </el-select>
      <el-select v-if="!isArticleInfo && !showCategorySelect" size="small" :style="{width: !isAutoWidth ? '32%' : '100%',float: !isAutoWidth ? 'left' : 'none',marginLeft: !isAutoWidth ? '5px' : '0'}" :disabled="shopDisabled" filterable v-model="form.shopId" placeholder="请选择店铺" @change="onChangeShop">
        <el-option v-for="(item, index) in shopList" :key="index" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <el-select v-if="!isArticleInfo && showCategorySelect" size="small" :style="{width: showSubCategorySelect ? '100%' : '66%',float: !isAutoWidth ? 'left' : 'none',marginLeft: !isAutoWidth ? '5px' : '0'}" :disabled="categoryDisabled" filterable v-model="form.categoryId" placeholder="请选择分类" @change="onChangeCategory">
        <el-option v-for="(item, index) in categoryList" :key="index" :label="item.name" :value="item.id">
          <div style="padding: 10px;">
            <div style="line-height: 1;">{{ item.name }}</div>
          </div>
        </el-option>
      </el-select>
      <el-select v-if="!isArticleInfo && showCategorySelect && showSubCategorySelect" size="small" :style="{width: '100%',float: !isAutoWidth ? 'left' : 'none',marginLeft: !isAutoWidth ? '5px' : '0'}" :disabled="subCategoryDisabled" filterable v-model="form.subCategoryId" placeholder="请选择子分类" @change="onChangeSubCategory">
        <el-option v-for="(item, index) in subCategoryList" :key="index" :label="item.name" :value="item.id">
          <div style="padding: 10px;">
            <div style="line-height: 1;">{{ item.name }}</div>
          </div>
        </el-option>
      </el-select>
      <el-select v-if="!isArticleInfo && !showCategorySelect" size="small" :style="{width: !isAutoWidth ? '33%' : '100%',float: !isAutoWidth ? 'left' : 'none',marginLeft: !isAutoWidth ? '5px' : '0'}" :disabled="spuDisabled" filterable v-model="form.spuId" placeholder="请选择商品" @change="onChangeSpu">
        <el-option v-for="(item, index) in spuList" :key="index" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <el-select v-if="isArticleInfo" size="small" :style="{width: !isAutoWidth ? '66%' : '100%',float: !isAutoWidth ? 'left' : 'none',marginLeft: !isAutoWidth ? '5px' : '0'}" filterable v-model="form.articleInfoId" placeholder="请选择文章" @change="onChangeArticle">
        <el-option v-for="(item, index) in articleInfoList" :key="index" :label="item.articleTitle" :value="item.id"></el-option>
      </el-select>
    </div>
    <el-input style="margin-top: 5px;" size="small" disabled placeholder="页面URL" v-model="pageTemp"></el-input>

  </div>
</template>

<script>


  import request from '@/router/axios'

  import { pageUrls } from '@/components/app-page-select/pageUrls'

  import * as shopinfo from '@/api/mall/shopinfo'
  import * as goodsspu from '@/api/mall/goodsspu'
  import * as bargaininfo from '@/api/mall/bargaininfo'
  import * as grouponinfo from '@/api/mall/grouponinfo'
  import * as seckillinfo from '@/api/mall/seckillinfo'
  import * as articleinfo from '@/api/mall/articleinfo'
  import {fetchTree as getTreeGoodsCategory} from '@/api/mall/goodscategory'

  export default {
    props:{
      page: [String],
      clientType: [String], // 当前自定义页面类型
      isGoodsPage:{//是否只选择商品
        type: Boolean,
        default: false,
      },
      isRefresh:{//是否根据url自动刷新，用于公众号选择链接时
        type: Boolean,
        default: false,
      },
      isAutoWidth: {
        type: Boolean,
        default: false,
      },
    },
    watch:{
      page(val,oldVal){
        if(val!=oldVal&&this.isRefresh){
          this.initPage();
        }
      },
      clientType(val,oldVal){

      },
    },
    mounted() {
      // this.initPage();
    },
    data(){
      return {
        pageUrls: JSON.parse(JSON.stringify(pageUrls)), //app 页面地址
        curType: '',
        pageTemp: '',//临时展示的url
        params: '', // URL参数部分
        shopDisabled: true,
        spuDisabled: true,
        categoryDisabled: true,
        subCategoryDisabled: true,
        showCategorySelect: false,
        showSubCategorySelect: false,
        isArticleInfo: false,// 文章 pages/article/article-info/index'
        articleInfoList: [], // 文章列表
        shopList: [], // 店铺列表
        spuList: [], // 店铺的商品列表
        categoryList: [], // 分类列表
        subCategoryList: [], // 子分类列表
        form: {
          page: '',
          pageType: '',
          shopId: '',
          spuId: '',
          categoryId: '',
          subCategoryId: '',
          articleInfoId: '',
        },
      }
    },
    created() {
      if(this.clientType==='MA'){
        // 如果是小程序的首页，那么可以选择小程序直播列表页面
        this.pageUrls.pages.push({
            name : '小程序直播列表',
            url  : '/pages/live/room-list/index',
            type : '单页面'
        })
      }
      this.initPage();
      this.getShopList(); // 加载所有店铺数据
    },
    methods:{
      updatePageTemp() {
        this.pageTemp = this.form.page + (this.params ? this.params : '');
        this.$emit('change', this.pageTemp);
      },
      initPage(){
        if(this.page){
          let pageTemp = JSON.parse(JSON.stringify(this.page));
          this.pageTemp = pageTemp;
          if(this.page.indexOf('http')!=-1){
            pageTemp = pageTemp.substring(this.page.indexOf('/pages/'),this.page.length);
          }
          let appIdIndex = pageTemp.indexOf('app_id');
          if(appIdIndex!=-1){
            pageTemp =  pageTemp.substring(0, appIdIndex-1);
          }
          // 解析URL参数-只有分类才这样处理-防止之前逻辑错乱
          const paramsIndex = pageTemp.indexOf('?');
          if(paramsIndex !== -1 && pageTemp.includes('pages/goods/goods-category/index')) {
            this.params = pageTemp.substring(paramsIndex);
            pageTemp = pageTemp.substring(0, paramsIndex);
          }
          if(pageTemp.includes('pages/goods/goods-category/index')) {
            this.form.page = pageTemp;
            this.showCategorySelect = true;
            this.categoryDisabled = false;
            this.shopDisabled = true;
            this.spuDisabled = true;

            // 从params中解析category和categorySon
            if(this.params) {
              const urlParams = new URLSearchParams(this.params);
              const category = urlParams.get('category');
              const categorySon = urlParams.get('categorySon');
              const categoryName = urlParams.get('categoryName');
              const categorySonName = urlParams.get('categorySonName');

              if(category) {
                this.form.categoryId = category;
              }
              if(categorySon) {
                this.form.subCategoryId = categorySon;
              }
            }
            this.getCategoryList();
          } else if(pageTemp.indexOf('id')!=-1) {
            let urlTemp = pageTemp.substring(0, pageTemp.indexOf('id=')+3);
            let idTemp =  pageTemp.substring(pageTemp.indexOf('id=')+3, pageTemp.length);
            this.form.page = urlTemp;
            if(this.page.indexOf('/extraJumpPages/shop/shop-detail/index?id=')!=-1) {//如果是店铺详情页，那么就把选择商品禁用
              this.form.shopId = idTemp;
              this.shopDisabled= false;
              this.spuDisabled= true;
              this.curType = this.pageUrls.types.shopDetailPage;
            } else if(pageTemp.includes('pages/goods/goods-category/index')) { // 如果是分类页面
              this.form.categoryId = idTemp;
              this.showCategorySelect = true;
              this.categoryDisabled = false;
              this.shopDisabled = true;
              this.spuDisabled = true;
              this.getCategoryList();
            } else {
              this.form.spuId = idTemp;
              this.pageUrls.pages.forEach((item,index)=>{
                if(item.url == urlTemp){//判断url类型
                  this.curType = item.type;
                  return;//跳出循环
                }
              });
              if(idTemp){
                if(this.curType==this.pageUrls.types.bargainDetailPage){//如果是 砍价详情页面 取砍价的商品列表
                  this.getBargaininfoDetail(idTemp);
                }else if(this.curType==this.pageUrls.types.grouponDetailPage){//如果是 拼团活动详情
                  this.getGrouponinfoDetail(idTemp);
                }else if(this.curType==this.pageUrls.types.goodsDetailPage){//如果是 商品详情
                  this.getGoodsSpuDetail(idTemp);
                }else if(this.curType==this.pageUrls.types.seckillDetailPage){//如果是 秒杀 详情
                  this.getSeckillDetail(idTemp);
                }
              }
              this.shopDisabled= false;
              this.spuDisabled= false;
            }
          } else {
            if(pageTemp.includes('pages/goods/goods-category/index')) { // 如果是分类列表页面
              this.showCategorySelect = true;
              this.categoryDisabled = false;
              this.shopDisabled = true;
              this.spuDisabled = true;
              this.getCategoryList();
            } else {
              this.shopDisabled= true;
              this.spuDisabled= true;
            }
            this.form.page = pageTemp;
          }
        } else {
          if(this.isGoodsPage){
            this.form.page = '/pages/goods/goods-detail/index'
            this.params = '?id=';
            this.onChangePage(this.form.page)
          }
        }
      },
      setPagePath(url){
        this.pageTemp = url;
      },
      handleSubmit(){},
      onChangePage(value){
        if(value){
          this.form.shopId = '';
          this.form.spuId = '';
          this.form.categoryId = '';
          this.form.subCategoryId = '';
          this.form.articleInfoId = '';
          this.params = '';

          if(value=='/pages/article/article-info/index?id=') {
            this.isArticleInfo = true;
            this.showCategorySelect = false;
            if(this.articleInfoList.length==0){
              articleinfo.getPage({}).then(response => {
                this.articleInfoList = response.data.data.records
              }).catch(() => {})
            }
          } else if(value.includes('pages/goods/goods-category/index')) {
            this.isArticleInfo = false;
            this.showCategorySelect = true;
            this.categoryDisabled = false;
            this.shopDisabled = true;
            this.spuDisabled = true;
            this.getCategoryList();
            this.form.page = '/pages/goods/goods-category/index';
          } else {
            this.isArticleInfo = false;
            this.showCategorySelect = false;
            this.pageUrls.pages.forEach((item,index)=>{
              if(item.url == value){
                this.curType = item.type;
                if(item.type!= this.pageUrls.types.singlePage){
                  this.shopDisabled = false;
                  if(item.type!=this.pageUrls.types.shopDetailPage){
                    this.spuDisabled = false;
                  }else {
                    this.spuDisabled = true;
                  }
                }else {
                  this.shopDisabled = true;
                  this.spuDisabled = true;
                }
                return;
              }
            });
            this.form.page = value;
          }
          this.updatePageTemp();
        }
      },
      onChangeShop(value){//更改店铺
        if(value){
          this.form.spuId = '';
          this.pageTemp = this.form.page+this.form.spuId;
          if(this.curType!=this.pageUrls.types.shopDetailPage){
            if(this.curType==this.pageUrls.types.bargainDetailPage){//如果是 砍价详情页面 取砍价的商品列表
              this.getBargaininfo();
            }else if(this.curType==this.pageUrls.types.grouponDetailPage){//如果是 拼团活动详情
              this.getGrouponinfo();
            }else if(this.curType==this.pageUrls.types.seckillDetailPage){//如果是 秒杀 详情
              this.getSeckillinfo();
            }else if(this.curType==this.pageUrls.types.goodsDetailPage){//如果是 商品详情
              this.getGoodsSpu();
            }
          }else {//如果是店铺
            this.pageTemp = this.form.page+this.form.shopId;
          }
          this.$emit('change', this.pageTemp);
        }
      },
      onChangeSpu(value){//更改店铺商品
        if(value){
          this.pageTemp = this.form.page + this.form.spuId;
          this.$emit('change', this.pageTemp);
          if(this.isGoodsPage){//如果只选择商品时，可能需要商品id
            const goods = this.spuList.find(_item => _item.id === this.form.spuId)
            this.$emit('changeGoods', goods);
          }
        }
      },
      onChangeArticle(value){//文章
        if(value){
          this.pageTemp = this.form.page + this.form.articleInfoId;
          this.$emit('change', this.pageTemp);
        }
      },
      getBargaininfo() {//获取店铺所有 砍价的商品
        bargaininfo.getPage({shopId:this.form.shopId}).then(response => {
          this.spuList = response.data.data.records
        }).catch(() => {
        })
      },
      getGrouponinfo() {//获取店铺所有 拼团的商品
        grouponinfo.getPage({shopId:this.form.shopId}).then(response => {
          this.spuList = response.data.data.records
        }).catch(() => {
        })
      },
      getSeckillinfo() {//获取店铺所有 秒杀 的商品
        seckillinfo.getPage({shopId: this.form.shopId}).then(response => {
          this.spuList = response.data.data.records
        }).catch(() => {
        })
      },
      getGoodsSku() {//获取店铺所有商品的规格
        // goodsSku.getPage({shopId:this.form.spuId}).then(response => {
        //   this.skuList = response.data.data.records
        // }).catch(() => {
        // })
      },
      getBargaininfoDetail(id) {//获取 砍价的商品详情,用于回显
        bargaininfo.getObj(id).then(response => {
          if(response.data){
            this.form.shopId = response.data.data.shopId;
            this.getBargaininfo();
          }
        }).catch(() => {
        })
      },
      getGrouponinfoDetail(id) {//获取店铺所有 拼团的商品详情，用于回显
        grouponinfo.getObj(id).then(response => {
          if(response.data){
            this.form.shopId = response.data.data.shopId;
            this.getGrouponinfo();
          }
        }).catch(() => {
        })
      },
      getGoodsSpuDetail(id) {//获取商品详情，用于回显
        goodsspu.getObj(id).then(response => {
          if(response.data){
            this.form.shopId = response.data.data.shopId;
            this.getGoodsSpu();
          }
        }).catch(() => {
        })
      },
      getSeckillDetail(id) {//获取 秒杀 详情，用于回显
        seckillinfo.getObj(id).then(response => {
          if(response.data){
            this.form.shopId = response.data.data.shopId;
            this.getSeckillinfo();
          }
        }).catch(() => {
        })
      },
      getGoodsSpu() {//获取店铺所有商品
        request({
          url: '/mall/goodsspu/list?shopId=' + this.form.shopId,
          method: 'get',
          params: {}
        }).then(response => {
          this.spuList = response.data;
        }).catch(() => {
        })
      },
      getShopList() {//获取所有店铺
        shopinfo.getList({}).then(response => {
          this.shopList = response.data.data
        }).catch(() => {
        })
      },
      onChangeCategory(value) {
        if(value) {
          this.form.subCategoryId = '';
          // 查找选中的分类
          const selectedCategory = this.categoryList.find(item => item.id === value);
          if(selectedCategory) {
            this.params = '?category=' + value + '&categoryName=' + encodeURIComponent(selectedCategory.name);
            if(selectedCategory.children && selectedCategory.children.length > 0) {
              this.showSubCategorySelect = true;
              this.subCategoryList = selectedCategory.children;
              this.subCategoryDisabled = false;
            } else {
              this.showSubCategorySelect = false;
              this.subCategoryList = [];
            }
          }
          this.updatePageTemp();
        }
      },
      onChangeSubCategory(value) {
        if(value) {
          const selectedSubCategory = this.subCategoryList.find(item => item.id === value);
          if(selectedSubCategory) {
            const parentCategory = this.categoryList.find(item => item.id === this.form.categoryId);
            const parentCategoryName = parentCategory ? parentCategory.name : '';
            this.params = '?category=' + this.form.categoryId +
                         '&categoryName=' + encodeURIComponent(parentCategoryName) +
                         '&categorySon=' + value +
                         '&categorySonName=' + encodeURIComponent(selectedSubCategory.name);
          }
          this.updatePageTemp();
        }
      },
      getCategoryList() {
        // 调用获取分类列表的接口
        getTreeGoodsCategory({}).then(response => {
          if(response.data && response.data.data) {
            this.categoryList = response.data.data.map(item => ({
              ...item,
              name: item.name || item.categoryName || '未命名分类'
            }));

            // 如果有初始值，检查是否需要显示子分类
            if(this.form.categoryId) {
              const selectedCategory = this.categoryList.find(item => item.id === this.form.categoryId);
              if(selectedCategory && selectedCategory.children && selectedCategory.children.length > 0) {
                this.showSubCategorySelect = true;
                this.subCategoryList = selectedCategory.children;
                this.subCategoryDisabled = false;
              }
            }
          } else {
            this.categoryList = [];
            console.warn('分类数据格式不正确:', response.data);
          }
        }).catch(() => {
          this.categoryList = [];
        })
      },
    }
  }
</script>

<style scoped>

</style>
