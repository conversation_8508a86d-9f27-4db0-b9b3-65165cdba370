<template>
    <div id="BaseMap" :style="[ { width: mw, height: mh } ]"></div>
</template>

<script name="BaseMap">
import initMapLoader from "@/util/map";

export default {
    props: {
        mw: {
            type: String,
            default: "100%"
        },
        mh: {
            type: String,
            default: "500px"
        }
    },
    data() {
        return {
            mapData: ""
        }
    },
    mounted() {
        this.initMap()
    },
    methods: {
        // 初始化地图
        async initMap() {
            let mapInfo = await initMapLoader();
            this.mapData = new mapInfo.Map('BaseMap');
            this.$emit('load', { AMap: mapInfo, mapData: this.mapData })
        }
    }
}
</script>

<style lang="scss" scoped>
.BaseMap {

}
</style>
