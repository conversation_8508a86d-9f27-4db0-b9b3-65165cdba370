<template>
    <div class="flex1 box-card">
        <div class="liketabTitle">销售额类别占比</div>
        <div class="timesearch">
            <el-radio-group v-model="radio" size="small">
                <el-radio-button label="today">今日</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">全年</el-radio-button>
            </el-radio-group>
            <div>
                <el-date-picker
                    size="small"
                    v-model="dateRange"
                    type="daterange"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :picker-options="pickerOptions"
                    @change="handleDateChange"
                >
                </el-date-picker>
            </div>
        </div>
        <!-- <div style="padding-top: 20px">
            <el-radio-group v-model="saleItype" @change="handleTypeChange">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="1">团购</el-radio-button>
                <el-radio-button label="2">秒杀</el-radio-button>
            </el-radio-group>
        </div> -->
        <div class="saleItypeEcharts" id="saleItypeEcharts"></div>
    </div>
</template>

<script>
import * as echarts from "echarts";
import { saleamount } from "@/api/mall/largescreen";
export default {
    name: "SalesCategory",
    data() {
        return {
            dateRange: [],
            saleItype: "",
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 7
                            );
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 30
                            );
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 90
                            );
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
            },
            radio: "week",
        };
    },
    mounted() {},
    watch: {
        radio: {
            handler(val) {
                this.handleRadioChange(val);
                this.getSaleAmount();
            },
            immediate: true,
        },
        dateRange: {
            handler(val) {},
            immediate: true,
        },
    },
    methods: {
        handleRadioChange(val) {
            if (val === "today") {
                this.dateRange = [
                    this.$moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
                ];
            } else if (val === "week") {
                this.dateRange = [
                    this.$moment()
                        .startOf("week")
                        .format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("week").format("YYYY-MM-DD HH:mm:ss"),
                ];
            } else if (val === "month") {
                this.dateRange = [
                    this.$moment()
                        .startOf("month")
                        .format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
                ];
            } else if (val === "year") {
                this.dateRange = [
                    this.$moment()
                        .startOf("year")
                        .format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("year").format("YYYY-MM-DD HH:mm:ss"),
                ];
            }
        },
        /**
         * 获取销售额
         */
        getSaleAmount() {
            saleamount(
                {
                    beginTime: this.dateRange[0],
                    endTime: this.dateRange[1],
                },
                false
            ).then((response) => {
                console.log(response.data.data, "response");
                this.goodsList = response.data.data.spu;
                this.initChart(response.data.data);
            });
        },
        initChart(data) {
            const chartDom = document.getElementById("saleItypeEcharts");
            const myChart = echarts.init(chartDom);
            const dataMap = {};
            data.category.forEach((item) => {
                dataMap[item.name || "--"] = {
                    value: item.ct,
                    percent: `${(
                        (item.ct /
                            data.category.reduce(
                                (acc, curr) => acc + curr.ct,
                                0
                            )) *
                        100
                    ).toFixed(2)}%`,
                };
            });

            const datas = Object.keys(dataMap).map((key) => ({
                name: key,
                value: dataMap[key].value,
            }));

            const option = {
                title: [
                    {
                        text: `¥${data.category
                            .reduce((acc, curr) => acc + curr.ct, 0)
                            .toFixed(2)}`,
                        top: "45%",
                        left: "26%",
                        padding: [15, 0, 0, -5],
                        textStyle: {
                            color: "#080404",
                            fontSize: 20,
                        },
                    },
                    {
                        text: "销售额",
                        top: "45%",
                        left: "28%",
                        padding: [-15, 0, 0, -15],
                        textStyle: {
                            color: "rgba(0,0,0,0.5)",
                            fontSize: 14,
                            fontWeight: "400",
                        },
                    },
                ],
                tooltip: {
                    trigger: "item",
                },
                legend: {
                    orient: "vertical",
                    right: "5%",
                    top: "middle",
                    itemGap: 20,
                    icon: "circle",
                    formatter: (name) => {
                        return `${name}  |  ${dataMap[name].percent}      ¥${dataMap[name].value}`;
                    },
                },
                series: [
                    {
                        name: "销售额类别",
                        type: "pie",
                        center: ["28%", "50%"],
                        radius: ["60%", "75%"],
                        avoidLabelOverlap: false,
                        padAngle: 5,
                        itemStyle: {
                            borderRadius: 0,
                        },
                        label: {
                            show: false,
                            position: "center",
                        },
                        emphasis: {
                            label: {
                                show: false,
                                fontSize: 40,
                                fontWeight: "bold",
                            },
                        },
                        labelLine: {
                            show: false,
                        },
                        data: datas,
                    },
                ],
            };

            myChart.setOption(option);
        },
        handleDateChange(val) {
            console.log("日期变化:", val);
            //   格式化一下-时间从00:00-23:59
            const beginTime = this.$moment(val[0])
                .startOf("day")
                .format("YYYY-MM-DD HH:mm:ss");
            const endTime = this.$moment(val[1])
                .endOf("day")
                .format("YYYY-MM-DD HH:mm:ss");

            console.log(beginTime, endTime);

            this.dateRange = [beginTime, endTime];
            console.log(this.dateRange);
            this.getSaleAmount();
        },
        handleTypeChange(val) {
            console.log("类型变化:", val);
        },
    },
};
</script>

<style lang="scss" scoped>
.flex1 {
    flex: 1;

    .liketabTitle {
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #e4e7ed;
        padding-left: 20px;
        font-weight: 800;
    }

    .timesearch {
        position: absolute;
        width: auto;
        display: flex;
        justify-content: space-between;
        top: 5px;
        right: 30px;
        height: 35px;
        align-items: center;
        font-size: 14px;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.7);
    }

    .saleItypeEcharts {
        height: 300px;
    }
}
</style>
