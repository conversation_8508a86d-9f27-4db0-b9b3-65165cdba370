<template>
    <div class="sy-nr-l">
        <div class="synrl-tit">待办实现</div>
        <div class="gnbox">
            <div class="gneve" @click="handleClick(0)">
                <div class="iconbox">
                    <i class="el-icon-s-order"></i>
                </div>
                <div class="dbbox">
                    <div class="dbnum">{{ status0 }}</div>
                    <div>待付款订单</div>
                </div>
            </div>
            <div class="gneve" @click="handleClick(1)">
                <div class="iconbox">
                    <i class="el-icon-s-order"></i>
                </div>
                <div class="dbbox">
                    <div class="dbnum">{{ status1 }}</div>
                    <div>待发货订单</div>
                </div>
            </div>
            <div class="gneve" @click="handleClick(2)">
                <div class="iconbox">
                    <i class="el-icon-s-order"></i>
                </div>
                <div class="dbbox">
                    <div class="dbnum">{{ status2 }}</div>
                    <div>待收货订单</div>
                </div>
            </div>
            <div class="gneve" @click="handleClick(4)">
                <div class="iconbox">
                    <i class="el-icon-s-order"></i>
                </div>
                <div class="dbbox">
                    <div class="dbnum">{{ status4 }}</div>
                    <div>待评价订单</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "TodoList",
    props: {
        status0: {
            type: Number,
            default: 0,
        },
        status1: {
            type: Number,
            default: 0,
        },
        status2: {
            type: Number,
            default: 0,
        },
        status4: {
            type: Number,
            default: 0,
        },
    },
    methods: {
        handleClick(index) {
            this.$store.commit("SET_ORDER_STATUS", index);
            this.$router.push({
                path: "/mall/order/orderinfo",
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.sy-nr-l {
    flex: 1;

    .synrl-tit {
        font-size: 16px;
        padding-bottom: 20px;
        font-weight: 800;
    }

    .gnbox {
        display: flex;
        align-items: center;

        .gneve {
            flex: 1;
            display: flex;
            margin-right: 25px;
            cursor: pointer;

            .iconbox {
                width: 60px;
                height: 60px;
                margin: 0 auto;
                font-size: 38px;
                border-radius: 50%;
                color: #fff;
                background: rgb(86, 153, 256);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .dbbox {
                height: 60px;
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
            }
        }
    }
}
</style>
