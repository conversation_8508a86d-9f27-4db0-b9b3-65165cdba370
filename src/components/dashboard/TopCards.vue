<template>
  <div class="sy-top">
    <div class="sy-t-e">
      <div class="e-head">
        <div class="sy-t-title">今日销售额</div>
      </div>
      <div class="sy-t-Num">
        {{ topCradInfo.saleInfo.saleDayAmount }}
      </div>
      <div class="sy-t-tjjs">
        <span>日同比</span>
        <i
          :class="topCradInfo.saleInfo.saleDayAmountGrowthRate > 0 ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
          :style="topCradInfo.saleInfo.saleDayAmountGrowthRate > 0 ? 'color: rgb(134, 208, 98)' : 'color: rgb(245, 34, 45)'"
        ></i>
        <span>{{ topCradInfo.saleInfo.saleDayAmountGrowthRate.toFixed(2) }}%</span>
        <span>日环比</span>
        <i
          :class="topCradInfo.saleInfo.saleDayAmountGrowthRateLastDay > 0 ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
          :style="topCradInfo.saleInfo.saleDayAmountGrowthRateLastDay > 0 ? 'color: rgb(134, 208, 98)' : 'color: rgb(245, 34, 45)'"
        ></i>
        <span>{{ topCradInfo.saleInfo.saleDayAmountGrowthRateLastDay.toFixed(2) }}%</span>
      </div>
      <div class="sy-t-e-f">
        <div>
          本月销售额
          <span>¥{{ topCradInfo.saleInfo.saleMonthAmount }}</span>
        </div>
      </div>
    </div>

    <div class="sy-t-e">
      <div class="e-head">
        <div class="sy-t-title">今日订单数</div>
      </div>
      <div class="sy-t-Num">
        {{ topCradInfo.orderInfo.orderDayCount }}
      </div>
      <div class="sy-t-tjjs">
        <span>日同比</span>
        <i
          :class="topCradInfo.orderInfo.orderDayCountGrowthRate > 0 ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
          :style="topCradInfo.orderInfo.orderDayCountGrowthRate > 0 ? 'color: rgb(134, 208, 98)' : 'color: rgb(245, 34, 45)'"
        ></i>
        <span>{{ topCradInfo.orderInfo.orderDayCountGrowthRate.toFixed(2) }}%</span>
        <span>日环比</span>
        <i
          :class="topCradInfo.orderInfo.orderDayCountGrowthRateLastDay > 0 ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
          :style="topCradInfo.orderInfo.orderDayCountGrowthRateLastDay > 0 ? 'color: rgb(134, 208, 98)' : 'color: rgb(245, 34, 45)'"
        ></i>
        <span>{{ topCradInfo.orderInfo.orderDayCountGrowthRateLastDay.toFixed(2) }}%</span>
      </div>
      <div class="sy-t-e-f">
        <div>
          本月订单数
          <span>{{ topCradInfo.orderInfo.orderMonthCount }}</span>
        </div>
      </div>
    </div>

    <div class="sy-t-e">
      <div class="e-head">
        <div class="sy-t-title">今日新增用户</div>
        <!-- <div><i class="el-icon-info"></i></div> -->
      </div>
      <div class="sy-t-Num">
        {{ topCradInfo.userInfo.userDayCount }}
      </div>
      <div class="sy-t-tjjs"></div>
      <div class="sy-t-e-f">
        <div>
          总共用户
          <span>{{ topCradInfo.userInfo.userCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TopCards',
  props: {
    topCradInfo: {
      type: Object,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.sy-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px;

  .sy-t-e {
    width: 14%;
    border: 1px solid rgba(233, 233, 233, 1);
    border-radius: 20px;
    padding: 20px 30px 10px;

    .e-head {
      display: flex;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.42);

      .sy-t-title {
        font-size: 16px;
      }
    }

    .sy-t-Num {
      margin-bottom: 22px;
      font-size: 30px;
      color: rgba(0, 0, 0, 0.8);
      margin-top: 8px;
    }

    .sy-t-tjjs {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.4);
      margin-bottom: 10px;

      span {
        padding-right: 10px;
      }
    }

    .sy-t-e-f {
      padding: 9px 2px 5px 2px;
      border-top: 1px solid rgba(233, 233, 233, 1);

      span {
        padding-left: 15px;
      }
    }
  }
}
</style>
