<template>
    <div class="box-card">
        <el-tabs v-model="activeName">
            <el-tab-pane label="销售额" name="first">
                <div class="disFlex">
                    <div class="showecharts">
                        <div class="">销售额趋势</div>
                        <div
                            id="saleMonthChart"
                            ref="saleMonthChart"
                            style="height: 360px"
                        ></div>
                    </div>
                    <div class="showPm">
                        <div class="">商品销售额排名</div>
                        <div>
                            <div
                                class="spxsepm"
                                v-for="(item, i) in goodsList"
                                :key="i"
                            >
                                <div
                                    :class="
                                        i < 3 ? 'pmindex active' : 'pmindex'
                                    "
                                >
                                    {{ i + 1 }}
                                </div>
                                <div class="pmname">{{ item.spu_name }}</div>
                                <div class="pmnum">{{ item.ct }}元</div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="订单数" name="second">
                <div class="disFlex">
                    <div class="showecharts">
                        <div class="">订单数趋势</div>
                        <div
                            id="orderMonthChart"
                            ref="orderMonthChart"
                            style="height: 360px"
                        ></div>
                    </div>
                    <div class="showPm">
                        <div class="">订单数排名</div>
                        <div>
                            <div
                                class="spxsepm"
                                v-for="(item, i) in goodsList"
                                :key="i"
                            >
                                <div
                                    :class="
                                        i < 3 ? 'pmindex active' : 'pmindex'
                                    "
                                >
                                    {{ i + 1 }}
                                </div>
                                <div class="pmname">{{ item.spu_name }}</div>
                                <div class="pmnum">
                                    {{ item.ct }}
                                    {{ activeName === "first" ? "元" : "单" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div class="timesearch">
            <!-- 单选 -->
            <el-radio-group v-model="radio" size="small">
                <el-radio-button label="today">今日</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">全年</el-radio-button>
            </el-radio-group>
            <div>
                <el-date-picker
                    size="small"
                    v-model="dateRange"
                    type="daterange"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :picker-options="pickerOptions"
                    @change="handleDateChange"
                >
                </el-date-picker>
            </div>
        </div>
    </div>
</template>

<script>
import * as echarts from "echarts";
import {
    statistics,
    ordercount,
    saleamount,
    spusale,
} from "@/api/mall/largescreen";
export default {
    name: "SalesTrend",
    data() {
        return {
            activeName: "first",
            dateRange: [],
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 7
                            );
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 30
                            );
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(
                                start.getTime() - 3600 * 1000 * 24 * 90
                            );
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
            },
            goodsList: [],
            radio: "month",
        };
    },
    mounted() {},
    watch: {
        radio: {
            handler(val) {
                this.handleRadioChange(val);
                if (this.activeName === "first") {
                    this.getSaleAmount();
                } else {
                    this.getOrderCount();
                }
            },
            immediate: true,
        },
        activeName: {
            handler(val) {
                if (val === "first") {
                    this.getSaleAmount();
                } else {
                    this.getOrderCount();
                }
            },
        },
    },
    methods: {
        /**
         * 获取销售额
         */
        getSaleAmount() {
            saleamount(
                {
                    beginTime: this.dateRange[0],
                    endTime: this.dateRange[1],
                },
                false
            ).then((response) => {
                console.log(response.data.data, "response");
                this.goodsList = response.data.data.spu;
                this.initChart(response.data.data);
            });
        },
        /**
         * 获取订单数
         */
        getOrderCount() {
            ordercount(
                {
                    beginTime: this.dateRange[0],
                    endTime: this.dateRange[1],
                },
                false
            ).then((response) => {
                console.log(response.data.data, "response");
                this.goodsList = response.data.data.spu;
                this.initOrderChart(response.data.data);
            });
        },
        /**
         *
         * 商品销售额排名
         */
        getGoodsSaleAmount() {
            spusale(
                {
                    beginTime: this.dateRange[0],
                    endTime: this.dateRange[1],
                },
                false
            ).then((response) => {
                console.log(response.data.data, "response");
            });
        },
        handleRadioChange(val) {
            if (val === "today") {
                this.dateRange = [
                    this.$moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
                ];
            } else if (val === "week") {
                this.dateRange = [
                    this.$moment()
                        .startOf("week")
                        .format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("week").format("YYYY-MM-DD HH:mm:ss"),
                ];
            } else if (val === "month") {
                this.dateRange = [
                    this.$moment()
                        .startOf("month")
                        .format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
                ];
            } else if (val === "year") {
                this.dateRange = [
                    this.$moment()
                        .startOf("year")
                        .format("YYYY-MM-DD HH:mm:ss"),
                    this.$moment().endOf("year").format("YYYY-MM-DD HH:mm:ss"),
                ];
            }
        },
        initChart(data) {
            const chartDom = document.getElementById("saleMonthChart");
            const myChart = echarts.init(chartDom);
            const xdata = data.shopInfo.map((item) => item.name);
            const ydata = data.shopInfo.map((item) => item.ct);
            //   添加哥hover的时候tooltip显示的值
            const tooltip = {
                trigger: "axis",
                formatter: function (params) {
                    return params[0].name + ": " + params[0].value + "元";
                },
            };

            const option = {
                grid: {
                    left: "5%",
                    right: "15%",
                    top: "12%",
                    bottom: "5%",
                },
                xAxis: {
                    type: "category",
                    data: xdata,
                },
                yAxis: {
                    type: "value",
                },
                color: "rgba(24, 144, 255,0.8)",
                series: [
                    {
                        name: "销售额趋势",
                        data: ydata,
                        barWidth: 30,
                        type: "bar",
                        label: {
                            show: true,
                            position: "top",
                        },
                    },
                ],
                tooltip: tooltip,
                height: "82%",
            };

            myChart.setOption(option);
        },
        initOrderChart(data) {
            const chartDom = document.getElementById("orderMonthChart");
            const myChart = echarts.init(chartDom);
            const xdata = data.shopInfo.map((item) => item.name);
            const ydata = data.shopInfo.map((item) => item.ct);
            //   添加哥hover的时候tooltip显示的值
            const tooltip = {
                trigger: "axis",
                formatter: function (params) {
                    return params[0].name + ": " + params[0].value + "单";
                },
            };

            const option = {
                grid: {
                    left: "5%",
                    right: "15%",
                    top: "12%",
                    bottom: "5%",
                },
                xAxis: {
                    type: "category",
                    data: xdata,
                },
                yAxis: {
                    type: "value",
                },
                color: "rgba(24, 144, 255,0.8)",
                series: [
                    {
                        name: "订单数趋势",
                        data: ydata,
                        barWidth: 30,
                        type: "bar",
                        label: {
                            show: true,
                            position: "top",
                        },
                    },
                ],
                tooltip: tooltip,
                height: "82%",
            };

            myChart.setOption(option);
        },
        handleDateChange(val) {
            // 处理日期变化，重新获取数据
            console.log("日期变化:", val);
            //   格式化一下-时间从00:00-23:59
            this.dateRange = [
                this.$moment(val[0])
                    .startOf("day")
                    .format("YYYY-MM-DD HH:mm:ss"),
                this.$moment(val[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss"),
            ];
            if(this.activeName === "first"){
                this.getSaleAmount();
            }else{
                this.getOrderCount();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.box-card {
    margin-top: 20px;
    padding: 0 30px;
    position: relative;

    .el-tabs__item {
        height: 50px;
        line-height: 50px;
        font-size: 18px;
    }

    .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
        padding-left: 20px;
    }

    .el-tabs__nav-wrap::after {
        height: 1px;
    }

    .disFlex {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;

        .showecharts {
            flex: 2.5;
        }

        .showPm {
            flex: 1;

            .spxsepm {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: rgba(0, 0, 0, 0.7);
                margin: 15px 0;

                .pmindex {
                    width: 25px;
                    height: 25px;
                    border-radius: 50%;
                    text-align: center;
                    line-height: 25px;
                    font-size: 13px;
                    background: rgb(240, 242, 245);

                    &.active {
                        background: rgb(49, 70, 89);
                        color: #fff;
                    }
                }

                .pmname {
                    padding: 0px 0px 0px 24px;
                    font-size: 14px;
                    flex: 1;
                }

                .pmnum {
                    font-size: 14px;
                }
            }
        }
    }

    .timesearch {
        position: absolute;
        width: auto;
        display: flex;
        justify-content: space-between;
        top: 5px;
        right: 30px;
        height: 35px;
        align-items: center;
        font-size: 14px;
        cursor: pointer;
        color: rgba(0, 0, 0, 0.7);
        > div {
            margin-left: 10px;
        }
    }
}
</style>
