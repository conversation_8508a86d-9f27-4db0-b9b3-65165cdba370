<template>
  <div class="sy-nr-r">
    <div class="synrl-tit">常用功能</div>
    <div class="gnbox">
      <div class="gneve" v-for="(item, index) in functions" :key="index" @click="handleClick(item)">
        <div class="iconbox">
          <i :class="item.icon"></i>
        </div>
        <div class="gn-tit">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonFunctions',
  data() {
    return {
      functions: [
        { title: '全部订单', icon: 'el-icon-s-order', path: '/mall/order/orderinfo' },
        { title: '全部商品', icon: 'el-icon-s-order', path: '/mall/goods/goodsspu' },
        { title: '售后订单', icon: 'el-icon-s-order', path: '/mall/order/orderrefunds' },
        { title: '店员管理', icon: 'el-icon-s-order', path: '/mall/shop/shopuser' },
        { title: '收支明细', icon: 'el-icon-s-order', path: '/mall/largescreen' },
        // { title: '客服工作台', icon: 'el-icon-s-order', path: '/customer-service' }
      ]
    }
  },
  methods: {
    handleClick(item) {
      this.$router.push(item.path)
    }
  }
}
</script>

<style lang="scss" scoped>
.sy-nr-r {
  flex: 1;

  .synrl-tit {
    font-size: 16px;
    padding-bottom: 20px;
    font-weight: 800;
  }

  .gnbox {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .gneve {
      flex: 1;
      text-align: center;
      cursor: pointer;
      margin-bottom: 20px;

      .iconbox {
        width: 60px;
        height: 60px;
        margin: 0 auto;
        font-size: 38px;
        border-radius: 50%;
        color: #fff;
        background: rgb(86, 153, 256);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .gn-tit {
        margin-top: 10px;
        text-align: center;
      }
    }
  }
}
</style>
