<template>
  <div class="flex1 box-card">
    <div class="liketabTitle">线上热门搜索</div>
    <div class="timesearch">
      <div>今日</div>
      <div>本周</div>
      <div>本月</div>
      <div>全年</div>
      <div>
        <el-date-picker
          size="small"
          v-model="dateRange"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          @change="handleDateChange"
        >
        </el-date-picker>
      </div>
    </div>

    <div class="searchCharts">
      <div id="ssyhsEcharts" class="s-e-a"></div>
      <div id="rjsscsEcharts" class="s-e-a"></div>
    </div>
    <div>
      <avue-crud
        ref="crud"
        :page="page"
        :data="tableData"
        :table-loading="tableLoading"
        :option="tableOption"
        @on-load="getPage"
        @refresh-change="getPage"
        @sort-change="sortChange"
        @search-change="searchChange"
      >
      </avue-crud>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SearchData',
  data() {
    return {
      dateRange: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      tableData: [],
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 20,
        ascs: [],
        descs: []
      },
      tableLoading: false,
      tableOption: {
        border: false,
        index: true,
        addBtn: false,
        menu: false,
        indexLabel: '排名',
        column: [
          {
            label: '搜索关键词',
            prop: 'title'
          },
          {
            label: '用户数',
            prop: 'usernum'
          },
          {
            label: '周涨幅',
            prop: 'zhangfu',
            sortable: true
          }
        ]
      }
    }
  },
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      this.initSearchUserChart()
      this.initSearchCountChart()
    },
    initSearchUserChart() {
      const chartDom = document.getElementById('ssyhsEcharts')
      const myChart = echarts.init(chartDom)

      const option = {
        title: {
          text: '搜索用户数',
          textStyle: {
            color: 'rgba(0,0,0,0.6)',
            fontSize: '14px'
          },
          left: 0,
          subtext: '{a|12.1%}',
          subtextStyle: {
            align: 'right',
            width: '200px',
            rich: {
              a: {
                width: '200px',
                padding: [20, 0, 0, 180]
              }
            }
          }
        },
        grid: {
          left: '3%',
          right: '15%',
          top: '40%',
          bottom: '5%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          show: false
        },
        yAxis: {
          type: 'value',
          name: '8,846',
          nameTextStyle: {
            fontSize: '22px',
            color: 'rgba(0,0,0,0.7)',
            padding: [0, 0, 0, 120]
          },
          nameGap: 0,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            symbol: 'none',
            smooth: true,
            lineStyle: {
              color: 'rgba(24, 144, 255,1)'
            },
            areaStyle: {
              color: 'rgba(24, 144, 255,0.2)'
            }
          }
        ]
      }

      myChart.setOption(option)
    },
    initSearchCountChart() {
      const chartDom = document.getElementById('rjsscsEcharts')
      const myChart = echarts.init(chartDom)

      const option = {
        title: {
          text: '人均搜索次数',
          textStyle: {
            color: 'rgba(0,0,0,0.6)',
            fontSize: '14px'
          },
          left: 0,
          subtext: '{a|17.1%}',
          subtextStyle: {
            align: 'right',
            width: '200px',
            rich: {
              a: {
                width: '200px',
                padding: [20, 0, 0, 180]
              }
            }
          }
        },
        grid: {
          left: '3%',
          right: '15%',
          top: '40%',
          bottom: '5%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          show: false
        },
        yAxis: {
          type: 'value',
          name: '5',
          nameTextStyle: {
            fontSize: '22px',
            color: 'rgba(0,0,0,0.7)',
            padding: [0, 0, 0, 120]
          },
          nameGap: 0,
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            data: [10, 20, 18, 87, 14, 55, 74],
            type: 'line',
            symbol: 'none',
            smooth: true,
            lineStyle: {
              color: 'rgba(123, 213, 223,1)'
            },
            areaStyle: {
              color: 'rgba(123, 213, 223,0.2)'
            }
          }
        ]
      }

      myChart.setOption(option)
    },
    handleDateChange(val) {
      console.log('日期变化:', val)
    },
    sortChange(val) {
      let prop = val.prop ? val.prop.replace(/([A-Z])/g, '_$1').toLowerCase() : ''
      if (val.order == 'ascending') {
        this.page.descs = []
        this.page.ascs = prop
      } else if (val.order == 'descending') {
        this.page.ascs = []
        this.page.descs = prop
      } else {
        this.page.ascs = []
        this.page.descs = []
      }
      this.getPage(this.page)
    },
    searchChange(params, done) {
      this.page.currentPage = 1
      this.getPage(this.page, params)
      done()
    },
    getPage(page, params) {
      // 实现获取数据的逻辑
      console.log('获取数据:', page, params)
    }
  }
}
</script>

<style lang="scss" scoped>
.flex1 {
  flex: 1;

  .liketabTitle {
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #e4e7ed;
    padding-left: 20px;
    font-weight: 800;
  }

  .timesearch {
    position: absolute;
    width: 520px;
    display: flex;
    justify-content: space-between;
    top: 5px;
    right: 30px;
    height: 35px;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.7);
  }

  .searchCharts {
    display: flex;
    justify-content: space-between;
    height: 160px;
    padding-top: 20px;

    .s-e-a {
      flex: 1;
    }
  }
}
</style>
