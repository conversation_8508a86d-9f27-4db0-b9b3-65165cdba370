// 全局变量
@import './variables.scss';
// 顶部右侧显示
@import './top.scss';
// 导航标签
@import './tags.scss';
// 工具类函数
@import './mixin.scss';
// 侧面导航栏
@import './sidebar.scss';
// 动画
@import './animate/vue-transition.scss';
//主题
@import './theme/index.scss';
//适配
@import './media.scss';
//Jo<PERSON><PERSON><PERSON> css
@import 'joolun.scss';
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 500;
}

@include scrollBar();
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
p,
blockquote,
th,
td {
    margin: 0;
    padding: 0;
}

* {
    outline: none !important;
}

body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

html,
body {
    height: 100%;
}

fieldset,
img {
    border: 0;
}

ol,
ul {
    list-style: none;
}

em {
    font-style: normal;
}

input,
button,
select,
textarea {
    outline: none;
}



i {
    display: inline-block;
}



a {
    color: inherit;
    text-decoration: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

.hidden {
    display: none;
}

.padding {
    padding: 0 !important;
}

.margin {
    margin: 0 !important;
}

.pull-right {
    float: right !important;
    ;
}

.pull-left {
    float: left !important;
}

.pull-center {
    text-align: center;
}

.pull-flex {
    display: flex;
}

.pull-overflow {
    overflow: hidden;
}

.pull-auto {
    height: auto;
    overflow: hidden;
}

.pull-height {
    height: 100%;
    overflow: hidden;
}

.pull-fixed {
    position: fixed;
    left: 0;
    top: 0;
}

.text-white {
    color: #fff;
}

.grayMode {
    filter: grayscale(100%);
}

.overflow_1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

// 覆盖 element-ui 的默认样式
.el-autocomplete-suggestion {
    width: auto !important;
}
