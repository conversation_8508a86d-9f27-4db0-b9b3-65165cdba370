@for $i from 1 through 100 {
	.wb#{$i} {
		width: $i * 1%;
	}
}

@for $i from 1 through 100 {
	.hb#{$i} {
		height: $i * 1%;
	}
}

@for $i from 1 through 100 {
	.whb#{$i} {
		width: $i * 1%;
		height: $i * 1%;
	}
}

@for $i from 1 through 750 {
	.wh-#{$i} {
		width: $i * 1px;
		height: $i * 1px;
	}
}

@for $i from 1 through 750 {
	.w-#{$i} {
		width: $i * 1px;
	}
}

@for $i from 1 through 750 {
	.h-#{$i} {
		height: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.whv-#{$i} {
		width: $i * 1vw;
		height: $i * 1vh;
	}
}

@for $i from 1 through 100 {
	.wv-#{$i} {
		width: $i * 1vw;
	}
}

@for $i from 1 through 100 {
	.hv-#{$i} {
		height: $i * 1vh;
	}
}

@for $i from 1 through 100 {
	.m-#{$i} {
		margin: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.m-t-#{$i} {
		margin-top: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.m-r-#{$i} {
		margin-right: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.m-b-#{$i} {
		margin-bottom: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.m-l-#{$i} {
		margin-left: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.p-#{$i} {
		padding: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.p-t-#{$i} {
		padding-top: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.p-r-#{$i} {
		padding-right: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.p-b-#{$i} {
		padding-bottom: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.p-l-#{$i} {
		padding-left: $i * 1px;
	}
}

@for $i from 1 through 100 {
	.fs-#{$i} {
		font-size: $i * 1px;
    line-height: $i * 1px;
	}
}

@for $i from 1 through 9 {
	.fw-#{$i} {
		font-weight: $i * 100;
	}
}

@for $i from 1 through 9 {
	.bor-#{$i} {
		font-weight: $i * 100;
	}
}

@for $i from 1 through 9 {
  .line#{$i} {
    overflow: hidden;
    display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
    -webkit-line-clamp: $i; /* 行数，值可以改，表示展示X行后多余的缩略展示 */
    -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
    word-break: break-all;
  }
}
