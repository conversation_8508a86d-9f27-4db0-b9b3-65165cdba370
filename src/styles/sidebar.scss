.avue-sidebar {
  padding-top: 10px;
  height: 100%;
  position: relative;
  background-color: #20222a;
  transition: width .6s;
  box-sizing: border-box;

  .el-menu {
    border-right: 0 !important;
  }

  .el-menu-item,
  .el-submenu__title {
    font-size: 13px;
    height: 52px;
    line-height: 52px;
    i {
      &:nth-child(1) {
        font-size: 22px;
        margin-right: 5px;
      }
    }
    .el-icon-arrow-down:before {
      font-size: 16px;
    }
  }

  .el-menu-item {
    i {
      font-size: 22px;
      margin-right: 5px;
    }

    &:hover {
      color: #fff;

      background-color: rgba(255, 255, 255, 0.12) !important;
      span,
      i {
        color: #fff;
      }
    }

    &.is-active {
      background-color: rgba(0, 0, 0, .8) !important;

      span,
      i {
        color: #fff;
      }

      &:hover {
        background-color: rgba(0, 0, 0, .8) !important;
      }

      &::before {
        content: " ";
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: $mainBg;
        position: absolute
      }
    }
  }

  .el-submenu__title {
    &:hover {
      i,
      span {
        color: #fff;
      }

      background-color: transparent !important;
    }
  }

  .el-submenu .el-menu-item {
    height: 45px;
    line-height: 45px;
    i {
      font-size: 20px;
      margin-right: 5px;
    }

    &.is-active {
      background-color: rgba(0, 0, 0, .8) !important;

      span,
      i {
        color: #fff
      }

      &:hover {
        background-color: rgba(0, 0, 0, .8) !important;
      }
    }

    &:hover {
      background-color: rgba(255, 255, 255, 0.12) !important;
      span,
      i {
        color: #fff;
      }
    }
  }
}
