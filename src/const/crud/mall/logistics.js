/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export const tableOption = {
  dialogDrag: false,
  border: true,
  indexLabel: '序号',
  stripe: true,
  menuAlign: 'center',
  align: 'center',
  menuType: 'text',
  searchShow: false,
  excelBtn: true,
  printBtn: true,
  viewBtn: true,
  labelWidth: 100,
  column: [
    {
      label: '物流编码',
      prop: 'code',
      editDisabled: true,
      rules: [
        {
          required: true,
          message: '物流编码不能为空',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '物流名',
      prop: 'name',
      rules: [
        {
          required: true,
          message: '物流名不能为空',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '是否显示',
      prop: 'enable',
      type: 'radio',
      slot: true,
      dicData: [{
        label: '关闭',
        value: '0'
      }, {
        label: '启用',
        value: '1'
      }],
      rules: [
        {
          required: true,
          message: '物流名不能为空',
          trigger: 'blur'
        }
      ]
    },
  ]
}
export const dicSelect = [
  {code: "shentong", name: '申通快递'},
  {code: "zhongtong", name: '中通快递'},
  {code: "yunda", name: '韵达快递'},
  {code: "shunfeng", name: '顺丰速运'},
  {code: "jd", name: '京东快递'},
  {code: "yundakuaiyun", name: '韵达快运'},
  {code: "shunfengkuaiyun", name: '顺丰快运'},
  {code: "yuantong", name: '圆通速递'},
  {
    code: "zhongtongkuaiyun",
    name: '中通快运'
  },
  {code: "jtexpress", name: '极兔速递'},
  {code: "youzhengguonei", name: '邮政快递包裹'},
  {code: "jingdongkuaiyun", name: '京东快运'},
  {code: "ems", name: 'EMS'},
  {code: "annengwuliu", name: '安能快运'},
  {code: "debangkuaidi", name: '德邦快递'},
  {code: "debangwuliu", name: '德邦物流'},
  {code: "huitongkuaidi", name: '百世快递'},
  {code: "baishiwuliu", name: '百世快运'},
  {code: "kuayue", name: '跨越速运'},
  {code: "pjbest", name: '品骏快递'},
  {code: "fengwang", name: '丰网速运'},
  {code: "xinfengwuliu", name: '信丰物流'},
  {code: "youshuwuliu", name: '优速快递'},
  {code: "yuantongguoji", name: '圆通国际'},
  {code: "zhaijisong", name: '宅急送'},
  {code: "ewe", name: 'EWE全球快递'},
  {code: "ytchengnuoda", name: '圆通承诺达'},
  {code: "weitepai", name: '微特派'},
  {code: "dsukuaidi", name: 'D速快递'},
  {code: "cfss", name: '银雁专送'},
  {code: "zhongyouex", name: '众邮快递'},
  {code: "idamalu", name: '大马鹿'},
  {code: "sxjdfreight", name: '顺心捷达'},
  {code: "yimidida", name: '壹米滴答'},
  {code: "jiayiwuliu", name: '佳怡物流'},
  {
    code: "jinguangsudikuaijian",
    name: '京广速递'
  },
  {code: "cccc58", name: '中集冷云'},
  {code: "nezhasuyun", name: '哪吒速运'},
  {code: "sunjex", name: '新杰物流'},
  {code: "gaotian56", name: '高田物流'}
];
