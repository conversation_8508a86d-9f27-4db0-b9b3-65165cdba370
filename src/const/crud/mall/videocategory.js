export const tableOption = {
    dialogDrag: true,
    border: true,
    indexLabel: '序号',
    stripe: true,
    menuAlign: 'center',
    align: 'center',
    menuType: 'text',
    searchShow: true,
    excelBtn: true,
    printBtn: true,
    viewBtn: true,
    searchMenuSpan: 6,
    searchMenuPosition:'left',
    addBtn:false,
    column: [
        {
            label: '分类名称',
            prop: 'vtName',
            search: true,
            sortable: true,
            searchOrder:4,
            rules: [
                {
                    required: true,
                    message: '请输入分类名称',
                    trigger: 'blur'
                },
                {
                    max: 100,
                    message: '长度在不能超过100个字符'
                },
            ]
        },
        {
            label: '序号',
            prop: 'sort',
            searchOrder:3,
            sortable: true,
            rules: [{
              required: true,
              message: '序号不能为空',
              trigger: 'blur'
            }]
          },
        {
            label: '分类图标',
            prop: 'vtIcon',
            sortable: true,
            searchOrder:1,
            dataType: 'array'
        },
        {
            label: '是否启用',
            prop: 'vtStatus',
            type: 'radio',
            searchOrder:2,
            search: true,
            sortable: true,
            span: 24,
            slot: true,
            rules: [{
              required: true,
              message: '请选择显示状态',
              trigger: 'blur'
            }],
            dicData: [{
              label: '启用',
              value: '0'
            }, {
              label: '禁用',
              value: '1'
            }]
          },
    ]
}
