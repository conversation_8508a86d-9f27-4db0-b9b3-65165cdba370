/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export const tableOption = {
    dialogDrag: true,
    border: true,
    indexLabel: '序号',
    stripe: true,
    menuAlign: 'center',
    align: 'center',
    menuType: 'text',
    searchShow: false,
    excelBtn: true,
    printBtn: true,
    viewBtn: true,
    searchMenuSpan: 6,
    column: [
            {
            label: '渠道id',
            prop: 'id',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入渠道id',
                        trigger: 'blur'
                    },
                                ]
        },
            {
            label: '渠道名称',
            prop: 'channelName',
            sortable: true,
            rules: [
                    {
                        required: true,
                        message: '请输入渠道名称',
                        trigger: 'blur'
                    },
                                    {
                        max: 255,
                        message: '长度在不能超过255个字符'
                    },
                ]
        },
            {
            label: '渠道简介',
            prop: 'channelDescription',
            sortable: true,
            rules: [
                                    {
                        max: 65535,
                        message: '长度在不能超过65535个字符'
                    },
                ]
        },
            {
            label: '渠道分类',
            prop: 'channelCategory',
            sortable: true,
            rules: [
                                    {
                        max: 100,
                        message: '长度在不能超过100个字符'
                    },
                ]
        },
            {
            label: '创建时间',
            prop: 'createdAt',
            sortable: true,
            rules: [
                                ]
        },
            {
            label: '修改时间',
            prop: 'updatedAt',
            sortable: true,
            rules: [
                                ]
        },
            {
            label: '二维码地址',
            prop: 'qcCodeUrl',
            sortable: true,
            rules: [
                                    {
                        max: 255,
                        message: '长度在不能超过255个字符'
                    },
                ]
        },
            {
            label: '二维码链接',
            prop: 'qcCodePath',
            sortable: true,
            rules: [
                                    {
                        max: 255,
                        message: '长度在不能超过255个字符'
                    },
                ]
        },
            ]
}
