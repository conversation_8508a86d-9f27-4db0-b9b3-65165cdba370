/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export const tableOption = {
    dialogDrag: true,
    border: true,
    index: true,
    indexLabel: "序号",
    stripe: true,
    menuAlign: "center",
    align: "center",
    menuType: "text",
    searchShow: false,
    excelBtn: true,
    printBtn: true,
    viewBtn: true,
    searchMenuSpan: 6,
    labelWidth: "100",
    column: [
        {
            label: "id",
            prop: "id",
            hide: true,
            editDisabled: true,
            addDisplay: false,
            rules: [
                {
                    required: true,
                    message: "请输入id",
                    trigger: "blur",
                },
            ],
        },
        {
            label: "配送员姓名",
            prop: "couriersName",
            slot: true,
            sortable: true,
            rules: [
                {
                    required: true,
                    max: 64,
                    message: "长度在不能超过64个字符",
                },
            ],
        },
        {
            label: "配送员电话",
            prop: "couriersPhone",
            slot: true,
            sortable: true,
            rules: [
                {
                    required: true,
                    max: 48,
                    message: "长度在不能超过48个字符",
                },
            ],
        },
        {
            label: "状态",
            prop: "status",
            type: "radio",
            slot: true,
            sortable: true,
            rules: [
                {
                    required: true,
                    message: '请选择状态',
                    trigger: 'blur'
                },
            ],
            dicData: [
                {
                    label: "正常",
                    value: "1",
                },
                {
                    label: "停用",
                    value: "0",
                },
            ],
        },
        {
            label: "创建时间",
            prop: "createTime",
            sortable: true,
            addDisplay: false,
            rules: [],
        },
        {
            label: "用户",
            prop: "uid",
            sortable: true,
            rules: [
                {
                    max: 64,
                    message: "长度在不能超过64个字符",
                },
            ],
        },
    ],
};
