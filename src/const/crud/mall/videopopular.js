import { format } from "crypto-js";

export const tableOption = {
    dialogType: 'drawer',
    border: true,
    stripe: true,
    menuAlign: 'center',
    align: 'center',
    menuType: 'text',
    dialogWidth: '88%',
    selection: true,
    searchMenuSpan: 6,
    index: true,
    indexLabel: '序号',
    searchShow: true,
    addBtn:false,
    excelBtn: true,
    printBtn: true,
    viewBtn: true,
    searchMenuSpan: 6,
    searchSpan: 4,
    searchMenuPosition:'left',
    editBtn:false,
    column: [
        {
            label: '视频标题',
            prop: 'title',
            search: true,
            rules: [
                {
                    required: true,
                    message: '请输入标题',
                    trigger: 'blur'
                },
                {
                    max: 100,
                    message: '长度在不能超过100个字符'
                },
            ]
        },
        {
            label: '播放量',
            prop: 'playNum',
            sortable: true,
            rules: [
                {
                    required: true,
                    message: '请选择视频分类',
                    trigger: 'blur'
                }
            ]
        },
        {
            label: '视频分类',
            prop: 'vtId',
            type: 'select',
            search: true,
            filterable: true,
            sortable: true,
            rules: [
                {
                    required: true,
                    message: '请选择视频分类',
                    trigger: 'blur'
                }
            ],
            props: {
                label: 'vtName',
                value: 'id'
            },
            dicUrl: '/mall/videocategory/list'
        },
        {
            label: '序号',
            prop: 'sort',
            sortable: true,
            rules: [
            ]
        },
        {
            label: '创建人',
            prop: 'userName',
            search: true,
            rules: [
            ]
        },
        {
            label: '视频主图',
            prop: 'picUrl',
            dataType: 'array',
            formslot: true,
            span: 24,
            hide: true,
            rules: [
                {
                    required: true,
                    message: '请上传视频主图',
                    trigger: 'blur'
                }
            ]
        },
        {
            label: '视频',
            prop: 'videoUrl',
            dataType: 'array',
            formslot: true,
            span: 24,
            hide: true,
            rules: [
                {
                    required: true,
                    message: '请上传视频',
                    trigger: 'blur'
                }
            ]
        },
        {
            label: '点赞数量',
            prop: 'likesNum',
            sortable: true,
            hide: true,
            rules: [
            ]
        },
        {
            label: '视频评论',
            prop: 'commentStatus',
            type: 'radio',
            hide: true,
            rules: [{
                required: true,
                message: '请选择显示状态',
                trigger: 'blur'
            }],
            dicData: [{
                label: '开启',
                value: '0'
            }, {
                label: '关闭',
                value: '1'
            }]
        },
        {
            label: '视频状态',
            prop: 'videoStatus',
            search: true,
            type: 'radio',
            rules: [
            ],
            dicData: [{
                label: '全部',
                value: ''
            }, {
                label: '未发布',
                value: '1'
            }, {
                label: '待审核',
                value: '2'
            }, {
                label: '已发布',
                value: '3'
            }, {
                label: '审核驳回',
                value: '4'
            }]
        },
        {
            label: '发布时间',
            prop: 'releaseTime',
            type: 'date',
            sortable: true,
            search: true,
            searchRange: true,
            searchSpan: 8,
            span: 12,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            rules: [
            ]
        },
        {
            label: '驳回原因',
            prop: 'reiect',
            rules: [
            ]
        },
        {
            label: '视频简介',
            prop: 'brief',
            sortable: true,
            hide: true,
            rules: [
            ]
        },
    ]
}
